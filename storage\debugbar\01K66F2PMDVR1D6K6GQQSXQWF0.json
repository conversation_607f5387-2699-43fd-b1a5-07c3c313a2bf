{"__meta": {"id": "01K66F2PMDVR1D6K6GQQSXQWF0", "datetime": "2025-09-27 21:50:51", "utime": **********.661496, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.089879, "end": **********.661506, "duration": 0.5716269016265869, "duration_str": "572ms", "measures": [{"label": "Booting", "start": **********.089879, "relative_start": 0, "end": **********.221533, "relative_end": **********.221533, "duration": 0.****************, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.221545, "relative_start": 0.*****************, "end": **********.661507, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.222886, "relative_start": 0.*****************, "end": **********.223078, "relative_end": **********.223078, "duration": 0.0001919269561767578, "duration_str": "192μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.53744, "relative_start": 0.*****************, "end": **********.53744, "relative_end": **********.53744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.54443, "relative_start": 0.*****************, "end": **********.54443, "relative_end": **********.54443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.549586, "relative_start": 0.45970702171325684, "end": **********.549586, "relative_end": **********.549586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.558157, "relative_start": 0.4682779312133789, "end": **********.558157, "relative_end": **********.558157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.561272, "relative_start": 0.4713928699493408, "end": **********.561272, "relative_end": **********.561272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.tables.result", "start": **********.565766, "relative_start": 0.4758870601654053, "end": **********.565766, "relative_end": **********.565766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.636341, "relative_start": 0.5464620590209961, "end": **********.636341, "relative_end": **********.636341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.638713, "relative_start": 0.5488338470458984, "end": **********.638713, "relative_end": **********.638713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.641978, "relative_start": 0.5520989894866943, "end": **********.641978, "relative_end": **********.641978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.64941, "relative_start": 0.5595309734344482, "end": **********.64941, "relative_end": **********.64941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.658072, "relative_start": 0.5681929588317871, "end": **********.658072, "relative_end": **********.658072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.661009, "relative_start": 0.5711300373077393, "end": **********.661319, "relative_end": **********.661319, "duration": 0.00030994415283203125, "duration_str": "310μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8031456, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.537401, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.544395, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.54955, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.5581, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.561239, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.tables.result", "param_count": null, "params": [], "start": **********.565717, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.phpfilament.tables.result", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fresult.blade.php&line=1", "ajax": false, "filename": "result.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.636309, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.638585, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.641944, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.649376, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.658032, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}]}, "queries": {"count": 223, "nb_statements": 220, "nb_visible_statements": 223, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11457000000000007, "accumulated_duration_str": "115ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 120 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.224873, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nsyhbB2bXjyBRJypBaDi2ILocmhzpijgwmGjiPkv' limit 1", "type": "query", "params": [], "bindings": ["nsyhbB2bXjyBRJypBaDi2ILocmhzpijgwmGjiPkv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.22536, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 1.562}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.231661, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 1.562, "width_percent": 0.628}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.2412882, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 2.191, "width_percent": 0.716}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` in (2, 3) and `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.244127, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 2.907, "width_percent": 0.576}, {"sql": "select * from `levels` where `levels`.`id` in (1, 2) and `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.2467482, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 3.483, "width_percent": 0.55}, {"sql": "select `school_sessions`.*, `registrations`.`user_id` as `laravel_through_key` from `school_sessions` inner join `registrations` on `registrations`.`school_session_id` = `school_sessions`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 91}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.252041, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:91", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=91", "ajax": false, "filename": "ResultResource.php", "line": "91"}, "connection": "racoed", "explain": null, "start_percent": 4.032, "width_percent": 0.585}, {"sql": "select `levels`.*, `registrations`.`user_id` as `laravel_through_key` from `levels` inner join `registrations` on `registrations`.`level_id` = `levels`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.2547362, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:110", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=110", "ajax": false, "filename": "ResultResource.php", "line": "110"}, "connection": "racoed", "explain": null, "start_percent": 4.617, "width_percent": 0.55}, {"sql": "select `semesters`.*, `registrations`.`user_id` as `laravel_through_key` from `semesters` inner join `registrations` on `registrations`.`semester_id` = `semesters`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 117}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.2581198, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:117", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=117", "ajax": false, "filename": "ResultResource.php", "line": "117"}, "connection": "racoed", "explain": null, "start_percent": 5.167, "width_percent": 0.655}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.2627928, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 5.822, "width_percent": 0.655}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.265434, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 6.476, "width_percent": 0.445}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = 1 and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", 1, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.2682152, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 6.922, "width_percent": 3.003}, {"sql": "select * from `applications` where `applications`.`user_id` = 22 and `applications`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 337}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 452}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.2749681, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:337", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=337", "ajax": false, "filename": "User.php", "line": "337"}, "connection": "racoed", "explain": null, "start_percent": 9.924, "width_percent": 0.698}, {"sql": "select * from `guardians` where `guardians`.`user_id` = 22 and `guardians`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 452}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.278377, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "User.php:344", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=344", "ajax": false, "filename": "User.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 10.622, "width_percent": 1.065}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = 1 and `level_id` = '1')) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [22, 1, "2", 1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 121}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.28169, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ManageResults.php:126", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=126", "ajax": false, "filename": "ManageResults.php", "line": "126"}, "connection": "racoed", "explain": null, "start_percent": 11.687, "width_percent": 0.628}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.285312, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 12.316, "width_percent": 0.463}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.287483, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 12.778, "width_percent": 0.471}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = 1 and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", 1, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.29097, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 13.25, "width_percent": 0.882}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = 1 and `level_id` = '1')) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [22, 1, "2", 1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 121}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.294455, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ManageResults.php:126", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=126", "ajax": false, "filename": "ManageResults.php", "line": "126"}, "connection": "racoed", "explain": null, "start_percent": 14.131, "width_percent": 0.637}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 82}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.298157, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:30", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=30", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "30"}, "connection": "racoed", "explain": null, "start_percent": 14.768, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.298804, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 14.768, "width_percent": 0.436}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.3010209, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 15.205, "width_percent": 0.489}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = 1 and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", 1, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.303516, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 15.693, "width_percent": 0.655}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = 1 and `level_id` = '1')) and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1, "2", 1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 70}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}], "start": **********.307362, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ManageResults.php:70", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=70", "ajax": false, "filename": "ManageResults.php", "line": "70"}, "connection": "racoed", "explain": null, "start_percent": 16.348, "width_percent": 0.751}, {"sql": "select `max_score`, `name` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 72}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}], "start": **********.310595, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ManageResults.php:72", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=72", "ajax": false, "filename": "ManageResults.php", "line": "72"}, "connection": "racoed", "explain": null, "start_percent": 17.099, "width_percent": 0.541}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = 1 and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", 1, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 192}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.312716, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 17.64, "width_percent": 0.445}, {"sql": "select * from `registrations` where `user_id` = 22 and `school_session_id` = '2' and `semester_id` = 1 and `level_id` = '1' limit 1", "type": "query", "params": [], "bindings": [22, "2", 1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 407}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 196}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.3148699, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:407", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=407", "ajax": false, "filename": "ResultResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 18.085, "width_percent": 0.515}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `semester_id` = 1 and `level_id` = '1' and `department_id` = '16' order by `code` asc", "type": "query", "params": [], "bindings": [1, "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.317216, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:207", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=207", "ajax": false, "filename": "ResultResource.php", "line": "207"}, "connection": "racoed", "explain": null, "start_percent": 18.6, "width_percent": 1.126}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 209}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.320357, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:209", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=209", "ajax": false, "filename": "ResultResource.php", "line": "209"}, "connection": "racoed", "explain": null, "start_percent": 19.726, "width_percent": 0.471}, {"sql": "select * from `scores` where `course_id` = 326 and `registration_id` = 35", "type": "query", "params": [], "bindings": [326, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.323419, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 20.197, "width_percent": 0.873}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.32663, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 21.07, "width_percent": 0.436}, {"sql": "select `total` from `total_scores` where `course_id` = 326 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [326, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.32866, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 21.507, "width_percent": 0.402}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 368}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}], "start": **********.33049, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:368", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 368}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=368", "ajax": false, "filename": "ResultResource.php", "line": "368"}, "connection": "racoed", "explain": null, "start_percent": 21.908, "width_percent": 0.305}, {"sql": "select * from `scores` where `course_id` = 327 and `registration_id` = 35", "type": "query", "params": [], "bindings": [327, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.332489, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 22.213, "width_percent": 0.498}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.334839, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 22.711, "width_percent": 0.41}, {"sql": "select `total` from `total_scores` where `course_id` = 327 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [327, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.336669, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 23.121, "width_percent": 0.349}, {"sql": "select * from `scores` where `course_id` = 328 and `registration_id` = 35", "type": "query", "params": [], "bindings": [328, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.338694, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 23.47, "width_percent": 0.873}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.342092, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 24.343, "width_percent": 0.471}, {"sql": "select `total` from `total_scores` where `course_id` = 328 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [328, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.344106, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 24.815, "width_percent": 0.454}, {"sql": "select * from `scores` where `course_id` = 329 and `registration_id` = 35", "type": "query", "params": [], "bindings": [329, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.346148, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 25.268, "width_percent": 0.384}, {"sql": "select * from `assessments` where `assessments`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.348033, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 25.652, "width_percent": 0.314}, {"sql": "select `total` from `total_scores` where `course_id` = 329 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [329, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.349901, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 25.967, "width_percent": 0.436}, {"sql": "select * from `scores` where `course_id` = 330 and `registration_id` = 35", "type": "query", "params": [], "bindings": [330, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.35195, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 26.403, "width_percent": 0.498}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.35398, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 26.901, "width_percent": 0.34}, {"sql": "select `total` from `total_scores` where `course_id` = 330 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [330, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.356343, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 27.241, "width_percent": 0.506}, {"sql": "select * from `scores` where `course_id` = 331 and `registration_id` = 35", "type": "query", "params": [], "bindings": [331, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.3594701, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 27.747, "width_percent": 0.594}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.361714, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 28.341, "width_percent": 0.332}, {"sql": "select `total` from `total_scores` where `course_id` = 331 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [331, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.363372, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 28.672, "width_percent": 0.384}, {"sql": "select * from `scores` where `course_id` = 332 and `registration_id` = 35", "type": "query", "params": [], "bindings": [332, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.365348, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 29.056, "width_percent": 0.567}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.367538, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 29.624, "width_percent": 0.358}, {"sql": "select `total` from `total_scores` where `course_id` = 332 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [332, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.3692431, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 29.982, "width_percent": 0.384}, {"sql": "select * from `scores` where `course_id` = 333 and `registration_id` = 35", "type": "query", "params": [], "bindings": [333, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.371164, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 30.366, "width_percent": 0.471}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.374302, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 30.837, "width_percent": 0.524}, {"sql": "select `total` from `total_scores` where `course_id` = 333 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [333, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.376774, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 31.361, "width_percent": 0.454}, {"sql": "select * from `scores` where `course_id` = 334 and `registration_id` = 35", "type": "query", "params": [], "bindings": [334, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.37885, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 31.815, "width_percent": 0.436}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.380857, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 32.251, "width_percent": 0.288}, {"sql": "select `total` from `total_scores` where `course_id` = 334 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [334, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.382787, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 32.539, "width_percent": 0.41}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 81}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.384798, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:381", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=381", "ajax": false, "filename": "ResultResource.php", "line": "381"}, "connection": "racoed", "explain": null, "start_percent": 32.949, "width_percent": 0.271}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 391}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 270}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 270}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 82}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}], "start": **********.386654, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:391", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=391", "ajax": false, "filename": "ResultResource.php", "line": "391"}, "connection": "racoed", "explain": null, "start_percent": 33.22, "width_percent": 0.323}, {"sql": "select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 306}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.388871, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:306", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 306}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=306", "ajax": false, "filename": "ResultResource.php", "line": "306"}, "connection": "racoed", "explain": null, "start_percent": 33.543, "width_percent": 0.838}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.39253, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 34.381, "width_percent": 0.681}, {"sql": "select `credit` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.395225, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:315", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=315", "ajax": false, "filename": "ResultResource.php", "line": "315"}, "connection": "racoed", "explain": null, "start_percent": 35.062, "width_percent": 0.698}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.398087, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 35.76, "width_percent": 0.402}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.400219, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 36.161, "width_percent": 0.323}, {"sql": "select `credit` from `courses` where `level_id` = 2 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [2, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.4022589, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:315", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=315", "ajax": false, "filename": "ResultResource.php", "line": "315"}, "connection": "racoed", "explain": null, "start_percent": 36.484, "width_percent": 0.794}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.405315, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 37.279, "width_percent": 0.951}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 277}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.408998, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:277", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=277", "ajax": false, "filename": "ResultResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 38.23, "width_percent": 0.41}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.411113, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 38.64, "width_percent": 0.393}, {"sql": "select `id`, `credit` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.413195, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:288", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=288", "ajax": false, "filename": "ResultResource.php", "line": "288"}, "connection": "racoed", "explain": null, "start_percent": 39.033, "width_percent": 0.759}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 326 limit 1", "type": "query", "params": [], "bindings": [35, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.415805, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 39.792, "width_percent": 0.402}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 327 limit 1", "type": "query", "params": [], "bindings": [35, 327], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.417793, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 40.194, "width_percent": 0.384}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 328 limit 1", "type": "query", "params": [], "bindings": [35, 328], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.41977, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 40.578, "width_percent": 0.41}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 329 limit 1", "type": "query", "params": [], "bindings": [35, 329], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.422087, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 40.988, "width_percent": 0.628}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 330 limit 1", "type": "query", "params": [], "bindings": [35, 330], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4252741, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 41.616, "width_percent": 0.567}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 331 limit 1", "type": "query", "params": [], "bindings": [35, 331], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.427555, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 42.184, "width_percent": 0.393}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 332 limit 1", "type": "query", "params": [], "bindings": [35, 332], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.429684, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 42.577, "width_percent": 0.489}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 333 limit 1", "type": "query", "params": [], "bindings": [35, 333], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4318929, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 43.065, "width_percent": 0.358}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 334 limit 1", "type": "query", "params": [], "bindings": [35, 334], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4339862, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 43.423, "width_percent": 0.393}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.436017, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 43.816, "width_percent": 0.34}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4385011, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 44.156, "width_percent": 0.594}, {"sql": "select `id`, `credit` from `courses` where `level_id` = 2 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [2, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.442387, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:288", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=288", "ajax": false, "filename": "ResultResource.php", "line": "288"}, "connection": "racoed", "explain": null, "start_percent": 44.75, "width_percent": 0.759}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 343 limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.44508, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 45.509, "width_percent": 0.62}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 344 limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.447416, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 46.129, "width_percent": 0.436}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 345 limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4495258, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 46.565, "width_percent": 0.358}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 346 limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.451488, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 46.923, "width_percent": 0.367}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 347 limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.453406, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 47.29, "width_percent": 0.428}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 348 limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.455706, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 47.718, "width_percent": 0.637}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 349 limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.458647, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 48.355, "width_percent": 0.524}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.46083, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 48.878, "width_percent": 0.358}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 1 and `school_session_id` = 2 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [1, 2, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.463201, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 49.236, "width_percent": 0.454}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 2 and `school_session_id` = 2 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [2, 2, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4653518, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 49.69, "width_percent": 0.375}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 1 and `school_session_id` = 3 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [1, 3, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.467326, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 50.065, "width_percent": 0.34}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 2 and `school_session_id` = 3 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [2, 3, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.469291, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 50.406, "width_percent": 0.489}, {"sql": "select `id`, `code` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 344}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.471682, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:344", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=344", "ajax": false, "filename": "ResultResource.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 50.895, "width_percent": 0.882}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 326) limit 1", "type": "query", "params": [], "bindings": [35, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.475245, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 51.776, "width_percent": 0.602}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 327) limit 1", "type": "query", "params": [], "bindings": [35, 327], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.4776878, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 52.378, "width_percent": 0.498}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 328) limit 1", "type": "query", "params": [], "bindings": [35, 328], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.479888, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 52.876, "width_percent": 0.358}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 329) limit 1", "type": "query", "params": [], "bindings": [35, 329], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.481786, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 53.234, "width_percent": 0.323}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 330) limit 1", "type": "query", "params": [], "bindings": [35, 330], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.48366, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 53.557, "width_percent": 0.323}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 331) limit 1", "type": "query", "params": [], "bindings": [35, 331], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.485696, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 53.88, "width_percent": 0.454}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 332) limit 1", "type": "query", "params": [], "bindings": [35, 332], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.4878109, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 54.334, "width_percent": 0.367}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 333) limit 1", "type": "query", "params": [], "bindings": [35, 333], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.4908202, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 54.7, "width_percent": 0.707}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.494477, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.407, "width_percent": 0.48}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4954538, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.887, "width_percent": 0.646}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.496609, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.533, "width_percent": 0.349}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.497342, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.882, "width_percent": 0.244}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4979398, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.127, "width_percent": 0.218}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.498477, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.345, "width_percent": 0.209}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4989898, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.554, "width_percent": 0.218}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4995089, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.773, "width_percent": 0.209}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500033, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.982, "width_percent": 0.218}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (?, ?, ?) on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.502882, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.2, "width_percent": 1.021}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 106}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.512261, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:39", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=39", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "39"}, "connection": "racoed", "explain": null, "start_percent": 59.221, "width_percent": 0}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = ? and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.514853, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.221, "width_percent": 0.454}, {"sql": "select * from `semesters` where `semesters`.`id` = ? and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.51597, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.675, "width_percent": 0.323}, {"sql": "select * from `levels` where `levels`.`id` = ? and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.516843, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.998, "width_percent": 0.271}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.517973, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.269, "width_percent": 0.515}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = ? and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.519123, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.784, "width_percent": 0.279}, {"sql": "select * from `semesters` where `semesters`.`id` = ? and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.519862, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.063, "width_percent": 0.279}, {"sql": "select * from `levels` where `levels`.`id` = ? and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.520597, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.342, "width_percent": 0.253}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5212421, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.596, "width_percent": 0.253}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5251389, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.849, "width_percent": 0.672}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.526238, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.521, "width_percent": 0.48}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `school_session_id` = ? and `semester_id` = ? and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.527356, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.001, "width_percent": 0.532}, {"sql": "select * from `users` where `id` = ? and `role` = ? and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?)) and `users`.`deleted_at` is null order by `users`.`id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.528819, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.533, "width_percent": 0.48}, {"sql": "select `semesters`.`name`, `semesters`.`id` from `semesters` inner join `registrations` on `registrations`.`semester_id` = `semesters`.`id` where `semesters`.`deleted_at` is null order by `semesters`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.547369, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.013, "width_percent": 0.532}, {"sql": "select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `is_active` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.55184, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.546, "width_percent": 0.576}, {"sql": "select * from `programmes` where `programmes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.553163, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.122, "width_percent": 0.419}, {"sql": "select * from `departments` where `id` in (?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.554024, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.541, "width_percent": 0.34}, {"sql": "select `name`, `id` from `departments` where `id` in (?) or `is_edu` = ? or `is_gse` = ? order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5547898, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.881, "width_percent": 0.375}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.566554, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.256, "width_percent": 0.585}, {"sql": "select `max_score`, `name` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.567487, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.841, "width_percent": 0.253}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5680509, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.094, "width_percent": 0.34}, {"sql": "select * from `registrations` where `user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.568714, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.435, "width_percent": 0.34}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `semester_id` = ? and `level_id` = ? and `department_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5694609, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.775, "width_percent": 0.646}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.570538, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.421, "width_percent": 0.271}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.571214, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.692, "width_percent": 0.454}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.572374, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.146, "width_percent": 0.541}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.573658, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.687, "width_percent": 0.506}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.575164, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.193, "width_percent": 0.707}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5764048, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.9, "width_percent": 0.375}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.577249, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.275, "width_percent": 0.34}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5780709, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.616, "width_percent": 0.506}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5790331, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.122, "width_percent": 0.279}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.579707, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.401, "width_percent": 0.34}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5805318, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.742, "width_percent": 0.349}, {"sql": "select * from `assessments` where `assessments`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.581399, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.091, "width_percent": 0.288}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.582211, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.379, "width_percent": 0.402}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.583129, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.78, "width_percent": 0.454}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.584026, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.234, "width_percent": 0.288}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.584725, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.522, "width_percent": 0.349}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.585556, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.871, "width_percent": 0.428}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.586411, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.299, "width_percent": 0.305}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5871391, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.604, "width_percent": 0.288}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.58789, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.892, "width_percent": 0.463}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.58921, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.355, "width_percent": 0.489}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5905132, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.844, "width_percent": 0.541}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.591869, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.385, "width_percent": 0.707}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.593111, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.092, "width_percent": 0.358}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.593922, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.45, "width_percent": 0.34}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.594769, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.79, "width_percent": 0.428}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5956302, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.218, "width_percent": 0.244}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.596288, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.462, "width_percent": 0.271}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5971382, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.733, "width_percent": 0.262}, {"sql": "select `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.597703, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.995, "width_percent": 0.48}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.59872, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.475, "width_percent": 0.271}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5992632, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.745, "width_percent": 0.244}, {"sql": "select `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.599782, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.99, "width_percent": 0.463}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.60075, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.452, "width_percent": 0.262}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.601244, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.714, "width_percent": 0.209}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.601819, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.924, "width_percent": 0.244}, {"sql": "select `id`, `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6023529, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.168, "width_percent": 0.498}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.603301, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.666, "width_percent": 0.271}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604044, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.936, "width_percent": 0.305}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6047392, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.242, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6055949, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.495, "width_percent": 0.637}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6071029, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.132, "width_percent": 0.532}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.608162, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.664, "width_percent": 0.375}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.608957, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.04, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.609591, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.293, "width_percent": 0.314}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610326, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.607, "width_percent": 0.271}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610985, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.878, "width_percent": 0.253}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.611508, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.131, "width_percent": 0.209}, {"sql": "select `id`, `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.612007, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.34, "width_percent": 0.48}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.612947, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.82, "width_percent": 0.332}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6136842, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.152, "width_percent": 0.262}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6143072, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.414, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614879, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.667, "width_percent": 0.227}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.615417, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.894, "width_percent": 0.236}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6159532, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.13, "width_percent": 0.236}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.616477, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.365, "width_percent": 0.209}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6169982, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.575, "width_percent": 0.244}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.617586, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.819, "width_percent": 0.227}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6181061, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.046, "width_percent": 0.279}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618679, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.325, "width_percent": 0.236}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6192012, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.561, "width_percent": 0.262}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.619812, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.823, "width_percent": 0.506}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.620829, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.329, "width_percent": 0.271}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.621488, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.6, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.622495, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.853, "width_percent": 0.515}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.623775, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.368, "width_percent": 0.541}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.62487, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.909, "width_percent": 0.384}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.625672, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.293, "width_percent": 0.262}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.626279, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.555, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6268651, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.808, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6274722, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.061, "width_percent": 0.244}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.628071, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.305, "width_percent": 0.489}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629045, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.794, "width_percent": 0.375}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629839, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.17, "width_percent": 0.367}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630598, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.536, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6311772, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.789, "width_percent": 0.236}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631725, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.025, "width_percent": 0.236}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.632264, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.261, "width_percent": 0.227}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.632803, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.487, "width_percent": 0.227}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637118, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.714, "width_percent": 0.55}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.645044, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.264, "width_percent": 0.454}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.645862, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.718, "width_percent": 0.297}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `school_session_id` = ? and `semester_id` = ? and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646771, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.015, "width_percent": 0.471}, {"sql": "select * from `users` where `id` = ? and `role` = ? and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?)) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.648165, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.486, "width_percent": 0.445}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.652466, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.931, "width_percent": 0.428}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653243, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.359, "width_percent": 0.297}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `school_session_id` = ? and `semester_id` = ? and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654489, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.656, "width_percent": 0.585}, {"sql": "select * from `users` where `id` = ? and `role` = ? and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?)) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656613, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.241, "width_percent": 0.759}]}, "models": {"data": {"App\\Models\\Course": {"value": 114, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 62, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\Grade": {"value": 37, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\Score": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FScore.php&line=1", "ajax": false, "filename": "Score.php", "line": "?"}}, "App\\Models\\Assessment": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FAssessment.php&line=1", "ajax": false, "filename": "Assessment.php", "line": "?"}}, "App\\Models\\Registration": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\User": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Department": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Level": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\Semester": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\Guardian": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGuardian.php&line=1", "ajax": false, "filename": "Guardian.php", "line": "?"}}, "App\\Models\\Programme": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=1", "ajax": false, "filename": "Programme.php", "line": "?"}}}, "count": 323, "is_counter": true}, "livewire": {"data": {"app.filament.student.resources.result-resource.pages.manage-results #6s6KikpX5hhrUthDUvao": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"result_filter\" => array:4 [\n        \"school_session_id\" => \"2\"\n        \"level_id\" => \"1\"\n        \"semester_id\" => 1\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => []\n    \"defaultActionArguments\" => []\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => null\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"result_filter\" => array:4 [\n        \"school_session_id\" => \"2\"\n        \"level_id\" => \"1\"\n        \"semester_id\" => 1\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.student.resources.result-resource.pages.manage-results\"\n  \"component\" => \"App\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults\"\n  \"id\" => \"6s6KikpX5hhrUthDUvao\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults@mountAction<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:159-212</a>", "middleware": "web", "duration": "585ms", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-435315662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-435315662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1723803565 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gqfRTjsDmYfSsFz8g6QX4srASvIuTkWXP33yHQbl</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1899 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;result_filter&quot;:[{&quot;school_session_id&quot;:&quot;2&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;semester_id&quot;:1,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:null,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;result_filter&quot;:[{&quot;school_session_id&quot;:&quot;2&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;semester_id&quot;:1,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;6s6KikpX5hhrUthDUvao&quot;,&quot;name&quot;:&quot;app.filament.student.resources.result-resource.pages.manage-results&quot;,&quot;path&quot;:&quot;student\\/results&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;69dd70957539b923b4367cdb78229cb4e120c35421cb429bc40762373575b2f3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">print</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723803565\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1337326741 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IjBJQXZrbUFPMzVYWlFvSCtJNnpRM1E9PSIsInZhbHVlIjoiYVVNb0QvOWVWOFFpdFJHdk1nR1M4MHAydGpVOGpKcFhrZVF0VXZ6TjRGWXlxZC9OeHhESmFpd016aW0wd2NiTWFxdHBJbWlvbU9VdGhDa3FMdVlmbWd4MWROQTZndGJoRzJxQUl5R25oWlE1MWZSZUI0dmdUV2xtdjNnbzFxbXkiLCJtYWMiOiJlOGVmYjFjYzMyZDlhYzk2ZmI5NmQ2MjZhMjE5ODRjZGQ0YzgxZjY1N2UxZDZiYjMyMmYxOTNjYTMxZDRiZTFjIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6Iitsdnd6WFJwK2dCTi9oOWcxQVJrcXc9PSIsInZhbHVlIjoiRkxHOHlKcnpzaU9zM1hvZFpuV1FzVkt0UHllTUNnaVVoMW1CaGRvbDZDVEZ5WlFtaWlkY1RuLzNWSlhnUFkyQ3pTNnI4elczZld2amt5bjVVbnE3aDhjbmtCdHRvTWF1YTNld1JYR0d6WG5vZW9PQTNRNzVXRG5pY0ZXanU3WGgiLCJtYWMiOiI5YzgzZWY2MDg4MTMyN2M1NmYxMzdjOGZhNjk5MDZlN2YyYWM5YzkzYTY2ZjZhYTQyNjNlYjY0ZmM4NzhmZjU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"220 characters\">https://portal.racoed.test/student/results?tableFilters[result_filter][school_session_id]=2&amp;tableFilters[result_filter][level_id]=1&amp;tableFilters[result_filter][semester_id]=1&amp;tableFilters[result_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2305</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337326741\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-251483912 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gqfRTjsDmYfSsFz8g6QX4srASvIuTkWXP33yHQbl</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nsyhbB2bXjyBRJypBaDi2ILocmhzpijgwmGjiPkv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251483912\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-113243883 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 27 Sep 2025 20:50:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113243883\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1157466367 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gqfRTjsDmYfSsFz8g6QX4srASvIuTkWXP33yHQbl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/student/results</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"142 characters\">https://portal.racoed.test/result/download/resultData_68d84c786c73c?signature=0752867f1ce6aa7b5ed89404c2a24fd241e124892dff0f8a3312296807524aeb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageResults_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>result_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157466367\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}