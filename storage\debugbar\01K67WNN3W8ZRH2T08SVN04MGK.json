{"__meta": {"id": "01K67WNN3W8ZRH2T08SVN04MGK", "datetime": "2025-09-28 11:07:38", "utime": **********.620663, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759054057.714391, "end": **********.620682, "duration": 0.9062910079956055, "duration_str": "906ms", "measures": [{"label": "Booting", "start": 1759054057.714391, "relative_start": 0, "end": **********.15571, "relative_end": **********.15571, "duration": 0.*****************, "duration_str": "441ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.155733, "relative_start": 0.****************, "end": **********.620685, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "465ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.158864, "relative_start": 0.****************, "end": **********.160472, "relative_end": **********.160472, "duration": 0.0016078948974609375, "duration_str": "1.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::99503e4f2086f10483464823e7fe02ed", "start": **********.257622, "relative_start": 0.****************, "end": **********.257622, "relative_end": **********.257622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.school-name", "start": **********.291667, "relative_start": 0.****************, "end": **********.291667, "relative_end": **********.291667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.309982, "relative_start": 0.5955910682678223, "end": **********.309982, "relative_end": **********.309982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3fea7b79a291f72028b3ade68edbac3", "start": **********.330654, "relative_start": 0.6162629127502441, "end": **********.330654, "relative_end": **********.330654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::df5d8c26b1fd4ecd22aeee145299adc3", "start": **********.529608, "relative_start": 0.8152170181274414, "end": **********.529608, "relative_end": **********.529608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.539105, "relative_start": 0.824713945388794, "end": **********.539105, "relative_end": **********.539105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.544823, "relative_start": 0.8304319381713867, "end": **********.544823, "relative_end": **********.544823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.5589, "relative_start": 0.8445091247558594, "end": **********.5589, "relative_end": **********.5589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.593048, "relative_start": 0.8786571025848389, "end": **********.593048, "relative_end": **********.593048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.analytics-tag", "start": **********.597452, "relative_start": 0.8830609321594238, "end": **********.597452, "relative_end": **********.597452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.607834, "relative_start": 0.8934431076049805, "end": **********.607908, "relative_end": **********.607908, "duration": 7.390975952148438e-05, "duration_str": "74μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.62004, "relative_start": 0.905648946762085, "end": **********.620168, "relative_end": **********.620168, "duration": 0.00012803077697753906, "duration_str": "128μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7093728, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "__components::99503e4f2086f10483464823e7fe02ed", "param_count": null, "params": [], "start": **********.257531, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/99503e4f2086f10483464823e7fe02ed.blade.php__components::99503e4f2086f10483464823e7fe02ed", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F99503e4f2086f10483464823e7fe02ed.blade.php&line=1", "ajax": false, "filename": "99503e4f2086f10483464823e7fe02ed.blade.php", "line": "?"}}, {"name": "filament.hooks.school-name", "param_count": null, "params": [], "start": **********.291582, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-name.blade.phpfilament.hooks.school-name", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fschool-name.blade.php&line=1", "ajax": false, "filename": "school-name.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.309893, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::b3fea7b79a291f72028b3ade68edbac3", "param_count": null, "params": [], "start": **********.330527, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/b3fea7b79a291f72028b3ade68edbac3.blade.php__components::b3fea7b79a291f72028b3ade68edbac3", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fb3fea7b79a291f72028b3ade68edbac3.blade.php&line=1", "ajax": false, "filename": "b3fea7b79a291f72028b3ade68edbac3.blade.php", "line": "?"}}, {"name": "__components::df5d8c26b1fd4ecd22aeee145299adc3", "param_count": null, "params": [], "start": **********.529538, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/df5d8c26b1fd4ecd22aeee145299adc3.blade.php__components::df5d8c26b1fd4ecd22aeee145299adc3", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fdf5d8c26b1fd4ecd22aeee145299adc3.blade.php&line=1", "ajax": false, "filename": "df5d8c26b1fd4ecd22aeee145299adc3.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.539032, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.544755, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.558826, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.592983, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament.hooks.analytics-tag", "param_count": null, "params": [], "start": **********.597329, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/analytics-tag.blade.phpfilament.hooks.analytics-tag", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fanalytics-tag.blade.php&line=1", "ajax": false, "filename": "analytics-tag.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01264, "accumulated_duration_str": "12.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.16866, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dVBvMM3LjMHjrlCGiqcN23uAtR5eqkDpsGaPI6kH' limit 1", "type": "query", "params": [], "bindings": ["dVBvMM3LjMHjrlCGiqcN23uAtR5eqkDpsGaPI6kH"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.170013, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 42.88}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiaHU5WUpiTnoyMXJyaEpPYU8yb1cwejhSdVpXdUNpVmpnb3UyYUtWTyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo0ODoiaHR0cHM6Ly9wb3J0YWwucmFjb2VkLnRlc3Qvc3R1ZGVudC9yZWdpc3RyYXRpb25zIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzI6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L2xvZ2luIjt9czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YToxOntzOjI2OiIwMUs2N1dOTTE5OVpDV1pGMVM1VkdDMU1GVCI7Tjt9fQ==', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36' where `id` = 'dVBvMM3LjMHjrlCGiqcN23uAtR5eqkDpsGaPI6kH'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiaHU5WUpiTnoyMXJyaEpPYU8yb1cwejhSdVpXdUNpVmpnb3UyYUtWTyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo0ODoiaHR0cHM6Ly9wb3J0YWwucmFjb2VkLnRlc3Qvc3R1ZGVudC9yZWdpc3RyYXRpb25zIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzI6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L2xvZ2luIjt9czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YToxOntzOjI2OiIwMUs2N1dOTTE5OVpDV1pGMVM1VkdDMU1GVCI7Tjt9fQ==", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "dVBvMM3LjMHjrlCGiqcN23uAtR5eqkDpsGaPI6kH"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.609114, "duration": 0.00722, "duration_str": "7.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "racoed", "explain": null, "start_percent": 42.88, "width_percent": 57.12}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": {"app.filament.auth.pages.login #1DgU6t2MJzaLv1TAn1Sr": "array:4 [\n  \"data\" => array:15 [\n    \"data\" => array:3 [\n      \"phone\" => null\n      \"password\" => null\n      \"remember\" => false\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.auth.pages.login\"\n  \"component\" => \"App\\Filament\\Auth\\Pages\\Login\"\n  \"id\" => \"1DgU6t2MJzaLv1TAn1Sr\"\n]", "filament.livewire.notifications #UNu1ABEWeZxo9gIPf3Jc": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2230\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"UNu1ABEWeZxo9gIPf3Jc\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/login", "action_name": "filament.auth.auth.login", "controller_action": "App\\Filament\\Auth\\Pages\\Login", "uri": "GET login", "domain": "https://portal.racoed.test", "controller": "App\\Filament\\Auth\\Pages\\Login@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:auth, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent", "duration": "901ms", "peak_memory": "10MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6ImFQNVV1RFM1aTBZK2ttdnFzV0pzSEE9PSIsInZhbHVlIjoiL05NdjNKMzRjUmJYdHJBMGhyY3pqbHRDZjIvaDZGNTJzc0ZKTGx3dHUvbkdjaHVvSVNzUUxTVDh2dWRPZmJ2ZERVMDR2dGFJSW1YMERHVTJyYU5pbGJDVk5uaW1LS2ZlOEVTUndJcTZoLzhOOTIzQklyYjZZOVBIdjRueDM0RGUiLCJtYWMiOiJjNGUzN2FkNjg5OWNlMWM1ZDFkY2VmZDk2ZTM3NmI5MmQxMDBhY2M0Y2I5MzQ2NDMzOGE0MWE2NzczMTIzMWFhIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IkIrbWUySkZKVndyVVRncWZnUUpRTHc9PSIsInZhbHVlIjoibjNnazVPTWVRNzJTODZTT0FrWHIrOXdyeHl1MGx0Wit6MWlkRVh4emdlR016WVA3WHJIb3VHK0kyWk5PbFhLcVYwYVBHS01MTi9id2FaM09ab3p4elVrUTFVNzJXcGhGclU5MjEvQVRyQ1J2MElXQ0FySWFTQUVZVURMaEZnQ0QiLCJtYWMiOiJkN2NkNWRiMTExYzVkZDAzMWY4MjU3ZTEyZTUyZGJhNTc4ZDQ5YjAxOTBmZmUzNTdlMDEwNTA1YzRkOGU2NGI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">https://portal.racoed.test/student/registrations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1958392581 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hu9YJbNz21rrhJOaO2oW0z8RuZWuCiVjgou2aKVO</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dVBvMM3LjMHjrlCGiqcN23uAtR5eqkDpsGaPI6kH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958392581\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1595590869 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 28 Sep 2025 10:07:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"457 characters\">XSRF-TOKEN=eyJpdiI6IlpRa2FZRWtWdmVTTENWb1doci95SkE9PSIsInZhbHVlIjoiZ0VvRjh2bERrS3hyN1lOdW9ZVDFDVjFhYWpjdjNQUFpXQXJtTGNkKzcwM0M1SGJuY3Qxdm5haHZ5MWZST0Q2MlQxNm9DOXAzZld3eVFGQ2M0MTdKUThWT2pTelJnWnBhc3VYR2ludHlyZmRHRGRyNEZkMjl2SkFDSXp0aDVEaXIiLCJtYWMiOiJhOWU5NzA3YjQ3ODkzZDBkNjEzMzViYjk1Y2M3Njk5YTMwNmE4MDM0ZDU4YzY4ZTcyYTgyODZjNWI3ZDdmZmU0IiwidGFnIjoiIn0%3D; expires=Sun, 28 Sep 2025 12:07:38 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"471 characters\">racoed_session=eyJpdiI6Im1QdlgweSt5eDRFTEI1SVZScnphVFE9PSIsInZhbHVlIjoiQk5pdWIrMEVWZDExYmFHY0xrWGtJNVI5SmxPdURpUTQwR051bWF2eDJ6V2Y5Vm56aU0wZS9xcVpDK1lFV0lRYzZaaEs1RG13S3FsWnRhc3BxMk5EWEl1N1hxZHlVVnNhejV0YjhiRFJVVkdjUi90MnNFN0RRTWdVZGo0WnZ1dGEiLCJtYWMiOiIzZjZjNGQxZTkzOTUxMGVlNjk1NmM4ODUwZjc5ZDUxYmZmOTlmN2NlYzVkYjk5ZDQ1ZjJhYzVhNmRmNzYyNjA5IiwidGFnIjoiIn0%3D; expires=Sun, 28 Sep 2025 12:07:38 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6IlpRa2FZRWtWdmVTTENWb1doci95SkE9PSIsInZhbHVlIjoiZ0VvRjh2bERrS3hyN1lOdW9ZVDFDVjFhYWpjdjNQUFpXQXJtTGNkKzcwM0M1SGJuY3Qxdm5haHZ5MWZST0Q2MlQxNm9DOXAzZld3eVFGQ2M0MTdKUThWT2pTelJnWnBhc3VYR2ludHlyZmRHRGRyNEZkMjl2SkFDSXp0aDVEaXIiLCJtYWMiOiJhOWU5NzA3YjQ3ODkzZDBkNjEzMzViYjk1Y2M3Njk5YTMwNmE4MDM0ZDU4YzY4ZTcyYTgyODZjNWI3ZDdmZmU0IiwidGFnIjoiIn0%3D; expires=Sun, 28-Sep-2025 12:07:38 GMT; domain=.racoed.test; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">racoed_session=eyJpdiI6Im1QdlgweSt5eDRFTEI1SVZScnphVFE9PSIsInZhbHVlIjoiQk5pdWIrMEVWZDExYmFHY0xrWGtJNVI5SmxPdURpUTQwR051bWF2eDJ6V2Y5Vm56aU0wZS9xcVpDK1lFV0lRYzZaaEs1RG13S3FsWnRhc3BxMk5EWEl1N1hxZHlVVnNhejV0YjhiRFJVVkdjUi90MnNFN0RRTWdVZGo0WnZ1dGEiLCJtYWMiOiIzZjZjNGQxZTkzOTUxMGVlNjk1NmM4ODUwZjc5ZDUxYmZmOTlmN2NlYzVkYjk5ZDQ1ZjJhYzVhNmRmNzYyNjA5IiwidGFnIjoiIn0%3D; expires=Sun, 28-Sep-2025 12:07:38 GMT; domain=.racoed.test; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595590869\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-835735308 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hu9YJbNz21rrhJOaO2oW0z8RuZWuCiVjgou2aKVO</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"48 characters\">https://portal.racoed.test/student/registrations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://portal.racoed.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K67WNM199ZCWZF1S5VGC1MFT</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835735308\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/login", "action_name": "filament.auth.auth.login", "controller_action": "App\\Filament\\Auth\\Pages\\Login"}, "badge": null}}