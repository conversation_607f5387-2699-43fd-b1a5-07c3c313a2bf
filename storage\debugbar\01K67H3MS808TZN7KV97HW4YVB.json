{"__meta": {"id": "01K67H3MS808TZN7KV97HW4YVB", "datetime": "2025-09-28 07:45:34", "utime": **********.120479, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759041932.992339, "end": **********.120498, "duration": 1.1281590461730957, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": 1759041932.992339, "relative_start": 0, "end": **********.782096, "relative_end": **********.782096, "duration": 0.****************, "duration_str": "790ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.782157, "relative_start": 0.****************, "end": **********.120502, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.818838, "relative_start": 0.****************, "end": **********.820982, "relative_end": **********.820982, "duration": 0.0021440982818603516, "duration_str": "2.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.tables.registrations", "start": **********.010015, "relative_start": 1.****************, "end": **********.010015, "relative_end": **********.010015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.026206, "relative_start": 1.****************, "end": **********.026206, "relative_end": **********.026206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.044241, "relative_start": 1.****************, "end": **********.044241, "relative_end": **********.044241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.059071, "relative_start": 1.0667321681976318, "end": **********.059071, "relative_end": **********.059071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.076965, "relative_start": 1.0846261978149414, "end": **********.076965, "relative_end": **********.076965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.095319, "relative_start": 1.102980136871338, "end": **********.095319, "relative_end": **********.095319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.108326, "relative_start": 1.1159870624542236, "end": **********.108326, "relative_end": **********.108326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.119635, "relative_start": 1.1272962093353271, "end": **********.120144, "relative_end": **********.120144, "duration": 0.0005087852478027344, "duration_str": "509μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7110128, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "filament.tables.registrations", "param_count": null, "params": [], "start": **********.009938, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/registrations.blade.phpfilament.tables.registrations", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fregistrations.blade.php&line=1", "ajax": false, "filename": "registrations.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.026133, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.044177, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.05897, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.076888, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.09524, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.108261, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}]}, "queries": {"count": 15, "nb_statements": 14, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.028549999999999996, "accumulated_duration_str": "28.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.836623, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = '8440Y34Bd5ZsZpI4FYdpE1CgYKG3KxlGlLXG6cgz' limit 1", "type": "query", "params": [], "bindings": ["8440Y34Bd5ZsZpI4FYdpE1CgYKG3KxlGlLXG6cgz"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.8377688, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 11.349}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.858465, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 11.349, "width_percent": 4.413}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 22 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 22, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.886738, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "racoed", "explain": null, "start_percent": 15.762, "width_percent": 24.448}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 22 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 22, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.90397, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "racoed", "explain": null, "start_percent": 40.21, "width_percent": 8.862}, {"sql": "select * from `users` where `users`.`id` = 22 and `users`.`role` = 1 and `users`.`deleted_at` is null order by `users`.`id` asc limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.960779, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 49.072, "width_percent": 11.909}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.972636, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 60.981, "width_percent": 3.538}, {"sql": "select * from `programmes` where `programmes`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.978039, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 64.518, "width_percent": 7.145}, {"sql": "select * from `levels` where `levels`.`id` in (1, 2) and `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9855769, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 71.664, "width_percent": 7.04}, {"sql": "select * from `semesters` where `semesters`.`id` in (1, 2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.991181, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 78.704, "width_percent": 2.487}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` in (2, 3) and `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.994785, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 81.191, "width_percent": 2.487}, {"sql": "select * from `invoices` where `fee_type` = 1 and `invoices`.`payable_id` in (20, 35, 36, 185) and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.000657, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 83.678, "width_percent": 5.534}, {"sql": "select * from `applications` where `applications`.`user_id` = 22 and `applications`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 378}, {"index": 22, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 10}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.096638, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:378", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 378}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=378", "ajax": false, "filename": "User.php", "line": "378"}, "connection": "racoed", "explain": null, "start_percent": 89.212, "width_percent": 3.538}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 386}, {"index": 15, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.103543, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:386", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=386", "ajax": false, "filename": "User.php", "line": "386"}, "connection": "racoed", "explain": null, "start_percent": 92.75, "width_percent": 3.853}, {"sql": "select * from `guardians` where `guardians`.`user_id` = 22 and `guardians`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": "view", "name": "filament.hooks.global-bio-data-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.php", "line": 13}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.110533, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:344", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=344", "ajax": false, "filename": "User.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 96.602, "width_percent": 3.398}]}, "models": {"data": {"App\\Models\\Registration": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Level": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\Semester": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\Programme": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=1", "ajax": false, "filename": "Programme.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\Guardian": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGuardian.php&line=1", "ajax": false, "filename": "Guardian.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"filament.livewire.database-notifications #oVRAdUKd8Dlabw2kPc4S": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => []\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"oVRAdUKd8Dlabw2kPc4S\"\n]", "app.filament.student.resources.registration-resource.pages.manage-registrations #m6hbIbJkJCmUfiYRiNN0": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => null\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.student.resources.registration-resource.pages.manage-registrations\"\n  \"component\" => \"App\\Filament\\Student\\Resources\\RegistrationResource\\Pages\\ManageRegistrations\"\n  \"id\" => \"m6hbIbJkJCmUfiYRiNN0\"\n]", "registration-toggle #oNymkVXPkCHcJlY5DucS": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2305\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 20\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 1\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 1\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 19:13:49\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 20\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 1\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 1\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 19:13:49\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2244\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2211\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2193\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2177\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => null\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => true\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"oNymkVXPkCHcJlY5DucS\"\n]", "registration-toggle #lIXBWIAGl0upYngRjf3x": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2267\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 35\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 1\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:35\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 35\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 1\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:35\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2244\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2208\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2193\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2173\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => App\\Models\\Invoice {#2153\n          #connection: \"mysql\"\n          #table: \"invoices\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 80\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 35\n            \"number\" => \"CPAY22072025081\"\n            \"reference\" => \"REF20250722203536-687FF61801776\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:16 [\n            \"id\" => 80\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 35\n            \"number\" => \"CPAY22072025081\"\n            \"reference\" => \"REF20250722203536-687FF61801776\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:9 [\n            \"total_amount\" => \"App\\Casts\\MoneyCast\"\n            \"invoice_status\" => \"App\\Enums\\InvoiceStatus\"\n            \"fee_type\" => \"App\\Enums\\FeeType\"\n            \"expired_at\" => \"datetime\"\n            \"paid_at\" => \"datetime\"\n            \"description\" => \"array\"\n            \"metadata\" => \"array\"\n            \"payment_success\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => false\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"lIXBWIAGl0upYngRjf3x\"\n]", "registration-toggle #ETAPVlDsKvtZdw1stlHF": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2322\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 36\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 2\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:36\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 36\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 2\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:36\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2244\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2208\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2195\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2173\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => App\\Models\\Invoice {#2155\n          #connection: \"mysql\"\n          #table: \"invoices\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 81\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 36\n            \"number\" => \"CPAY22072025082\"\n            \"reference\" => \"REF20250722203536-687FF61805037\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:16 [\n            \"id\" => 81\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 36\n            \"number\" => \"CPAY22072025082\"\n            \"reference\" => \"REF20250722203536-687FF61805037\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:9 [\n            \"total_amount\" => \"App\\Casts\\MoneyCast\"\n            \"invoice_status\" => \"App\\Enums\\InvoiceStatus\"\n            \"fee_type\" => \"App\\Enums\\FeeType\"\n            \"expired_at\" => \"datetime\"\n            \"paid_at\" => \"datetime\"\n            \"description\" => \"array\"\n            \"metadata\" => \"array\"\n            \"payment_success\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => false\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"ETAPVlDsKvtZdw1stlHF\"\n]", "registration-toggle #m6SBwKjsCaWEW00K8Leb": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2237\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 185\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 2\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-09-15 19:59:03\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 185\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 2\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-09-15 19:59:03\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2244\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2211\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2195\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2177\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => null\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => false\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"m6SBwKjsCaWEW00K8Leb\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.13s", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1372914596 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1372914596\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1444734460 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wW2oakzNb9e6FvC63LJYnnfnds4myZhJtt6SdRuH</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"356 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;oVRAdUKd8Dlabw2kPc4S&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;student\\/registrations&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;9c21b0714500f99102c659058228ed350cefa06e8de79e3337c4708fd21b605a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6Ik9OQjQxbVVJWGJIM1JBTHBYeEJzIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJmYTc3ZjY4MGRhY2MyNTEyNjY5MmI5YjgzODY5MTRhMGIzNTkxNWFhNDZjYjg4MTAxYjI0MTk1NWQ0NmMyNzcyIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1656 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:null,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:null,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;m6hbIbJkJCmUfiYRiNN0&quot;,&quot;name&quot;:&quot;app.filament.student.resources.registration-resource.pages.manage-registrations&quot;,&quot;path&quot;:&quot;student\\/registrations&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f23c54076589e832dbf430962c828ec01271e65061a3ad9bc48e84208319f132&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">loadTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444734460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1587315537 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IjJpT3JLOVk0SU1zamUwVVFCVXZKMFE9PSIsInZhbHVlIjoibzkxUWJmWUVXQWlLMVFzaTBqbWJpSkxpMHk4dDJQekZyWVM0SWE4eEpRZFJTSGkxWXNWSVV3SFNGc0t1UjUvS3prNmQvaWFjelFxTWtadVVFKzU3TDd1b0N1N05zZnZSb1BDL25hYmdmSHIyL3N3UVlPQTExZVZpYzR6RVZvOXMiLCJtYWMiOiIyZDNkMTE2Mjc1Y2Q3YjUzZWM3NjEzODE4N2YyYzhmMzZjOTE2YjUxM2U4ZWViZTljMjM3OTA1ZmIyZDNhNDY0IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IkxEdG01RnowUDJMYVRmYlFaS2s2c3c9PSIsInZhbHVlIjoiaXBCUDRRNUZSejVUbzVSSXQxaUlPeHBNdlA1aFJubVF3UGQ2Mm5OdXg5czdNMkFrYU5odytnbk5oZ2l4Qi94N2Y3NzFZRVFzTVFFczdpTExHNnFUZUF5cVlBbGtjV2NveC9rUmhGa1pNaWpIUE9aM3pkc1BSeXFzWW1OckpVNngiLCJtYWMiOiIyNmY1MTY5NGQ1YzJiN2ZiY2QyNjAzMmZiZmUwNTAwOTQ2YmEzYmIzMmIyZjE4N2NjOWYwODYzYzhhYWRiZDJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">https://portal.racoed.test/student/registrations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2735</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587315537\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2135829440 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wW2oakzNb9e6FvC63LJYnnfnds4myZhJtt6SdRuH</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8440Y34Bd5ZsZpI4FYdpE1CgYKG3KxlGlLXG6cgz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135829440\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-961588964 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 28 Sep 2025 06:45:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961588964\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1782268590 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wW2oakzNb9e6FvC63LJYnnfnds4myZhJtt6SdRuH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"252 characters\">https://portal.racoed.test/student/results?tableFilters%5Bresult_filter%5D%5Bschool_session_id%5D=2&amp;tableFilters%5Bresult_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">https://portal.racoed.test/student/registrations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K67EZ0ENP6GKSYJZBY8G6N6Z</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageResults_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>result_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>ManageRegistrations_filters</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782268590\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}