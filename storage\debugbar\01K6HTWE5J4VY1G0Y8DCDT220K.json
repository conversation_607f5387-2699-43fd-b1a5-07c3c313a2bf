{"__meta": {"id": "01K6HTWE5J4VY1G0Y8DCDT220K", "datetime": "2025-10-02 07:48:48", "utime": **********.050272, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.1954, "end": **********.050296, "duration": 1.854896068572998, "duration_str": "1.85s", "measures": [{"label": "Booting", "start": **********.1954, "relative_start": 0, "end": **********.636312, "relative_end": **********.636312, "duration": 0.*****************, "duration_str": "441ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.636329, "relative_start": 0.*****************, "end": **********.050299, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.643272, "relative_start": 0.****************, "end": **********.646153, "relative_end": **********.646153, "duration": 0.0028810501098632812, "duration_str": "2.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.048374, "relative_start": 1.****************, "end": **********.049333, "relative_end": **********.049333, "duration": 0.0009591579437255859, "duration_str": "959μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7102792, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 22, "nb_statements": 21, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06887999999999998, "accumulated_duration_str": "68.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.654139, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'BDtu4Y8py3R6PKJGi1LD5k3xRDDJGyzjycgmsS7e' limit 1", "type": "query", "params": [], "bindings": ["BDtu4Y8py3R6PKJGi1LD5k3xRDDJGyzjycgmsS7e"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.655941, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 7.303}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.686745, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 7.303, "width_percent": 1.481}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.044277, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 8.783, "width_percent": 2.671}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.055258, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 11.455, "width_percent": 1.916}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_start` asc", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 26}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.069459, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:26", "source": {"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=26", "ajax": false, "filename": "AcademicCalendarService.php", "line": "26"}, "connection": "racoed", "explain": null, "start_percent": 13.371, "width_percent": 2.381}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.08096, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 15.752, "width_percent": 2.613}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-02' and date(`semester_end`) >= '2025-10-02' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02", "2025-10-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.088708, "duration": 0.03532, "duration_str": "35.32ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 18.365, "width_percent": 51.278}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 40}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.129337, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 69.643, "width_percent": 4.036}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-02' and date(`semester_end`) >= '2025-10-02' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02", "2025-10-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.13672, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 73.679, "width_percent": 1.771}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.142234, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 75.45, "width_percent": 1.553}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.151532, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 77.003, "width_percent": 1.728}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and `semester_start` > '2025-10-02 07:48:47' order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02 07:48:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 78}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.1577501, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:78", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=78", "ajax": false, "filename": "AcademicCalendarService.php", "line": "78"}, "connection": "racoed", "explain": null, "start_percent": 78.731, "width_percent": 1.96}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, {"index": 17, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 84}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}], "start": 1759387727.166298, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:206", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=206", "ajax": false, "filename": "AcademicCalendarService.php", "line": "206"}, "connection": "racoed", "explain": null, "start_percent": 80.691, "width_percent": 1.873}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 89}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.173587, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:89", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=89", "ajax": false, "filename": "AcademicCalendarService.php", "line": "89"}, "connection": "racoed", "explain": null, "start_percent": 82.564, "width_percent": 3.078}, {"sql": "select * from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.185987, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:95", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=95", "ajax": false, "filename": "AcademicCalendarService.php", "line": "95"}, "connection": "racoed", "explain": null, "start_percent": 85.642, "width_percent": 2.091}, {"sql": "select * from `semesters` where `semesters`.`id` in (1) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 25, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.2004678, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:95", "source": {"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=95", "ajax": false, "filename": "AcademicCalendarService.php", "line": "95"}, "connection": "racoed", "explain": null, "start_percent": 87.732, "width_percent": 2.671}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.207924, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 90.404, "width_percent": 1.611}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 and `semester_start` > '2025-10-02 07:48:47' order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02 07:48:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 113}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.2174299, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:113", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=113", "ajax": false, "filename": "AcademicCalendarService.php", "line": "113"}, "connection": "racoed", "explain": null, "start_percent": 92.015, "width_percent": 1.844}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, {"index": 17, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 119}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}], "start": 1759387727.224734, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:206", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=206", "ajax": false, "filename": "AcademicCalendarService.php", "line": "206"}, "connection": "racoed", "explain": null, "start_percent": 93.859, "width_percent": 1.713}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.235412, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:124", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=124", "ajax": false, "filename": "AcademicCalendarService.php", "line": "124"}, "connection": "racoed", "explain": null, "start_percent": 95.572, "width_percent": 2.57}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 129}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Widgets\\AcademicCalendar.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": 1759387727.241497, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:129", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=129", "ajax": false, "filename": "AcademicCalendarService.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 98.142, "width_percent": 1.858}]}, "models": {"data": {"App\\Models\\SchoolSession": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Semester": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.student.widgets.academic-calendar #sxCD6jkoypyF3wr2qLvO": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.student.widgets.academic-calendar\"\n  \"component\" => \"App\\Filament\\Student\\Widgets\\AcademicCalendar\"\n  \"id\" => \"sxCD6jkoypyF3wr2qLvO\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.86s", "peak_memory": "10MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1687803104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1687803104\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-857945864 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DM6PBvh3hjhipPKy7EoVs4kxabeeTIrXhC6yRtUs</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"324 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;sxCD6jkoypyF3wr2qLvO&quot;,&quot;name&quot;:&quot;app.filament.student.widgets.academic-calendar&quot;,&quot;path&quot;:&quot;student\\/home&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;34ff6a7aab30532a1da658d5a3210ddb80711c5268fd22427357986c97cd20dc&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IlZmajlsRllZamZ5elNCREFwQkZ2IiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiIzNTI4YmYwY2ZkN2I0MGY3YWI4Y2U5MGEwN2NiODQxYWJmNjk1MDEwYmM1YzNmZjMzMmZkNTI0MGIyOTI5ZDVhIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857945864\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2066887000 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IjZ1aTVMbnVtL1FVM3NDRTlocnBTcVE9PSIsInZhbHVlIjoiUm5QakNpRlR5YURxQURhbG0vVmdKZHgwc0I5VnJyZHBJK0NoVEd1YlJOWkFZSGdNd1BNT3pnNjR0TVREWC9OaHFWN0dGUVFXRlY0Q05VSEg4SFVFQklod2R6cjRkSjNZZFBvTXR6cktUb2ZRbFdyK3ZWb3dZVG9QSG9CNHNwMzUiLCJtYWMiOiJlNmRiMTk4NzhjZGI0MzNmM2EwY2M5NjljOTI0YmQ1YTZmOTMzZjgzODM4NmQxODVmNTYxMjA1NmEyYzhiZjQ1IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IjVGa2ViQ3NMdjhBQll2YUFzVTlWdXc9PSIsInZhbHVlIjoiNXZwNkhNa1lEWmZaQnNOeVg4ekd6bHp6OTlneGVDejQxWTE3dFlVaUU0UzQxbDYyMS9zNkl3MTV5ZGtDWlhJR0RrV1ZmcGJrNWJZbXlFMy9CeHRlK2lWWWp6S3hYVGJ3YTZ1SS9qano0U2lHWGpjS3JFd1l6eG5NQnhmSUhxaTIiLCJtYWMiOiIxZmM5YmNlOTlhNjc2Nzg1OTNiYjcxN2Y3YTUyZmY0NDU4MDRhNTgyZWJkNTU3ZWIzNmI3Zjk3YWVjY2E3MjQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">https://portal.racoed.test/student/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">768</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066887000\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1444605978 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DM6PBvh3hjhipPKy7EoVs4kxabeeTIrXhC6yRtUs</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BDtu4Y8py3R6PKJGi1LD5k3xRDDJGyzjycgmsS7e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444605978\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-409064079 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 02 Oct 2025 06:48:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409064079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-953228525 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DM6PBvh3hjhipPKy7EoVs4kxabeeTIrXhC6yRtUs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K6HTS3T3WNY17F4261MXY401</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://portal.racoed.test/student/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953228525\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}