@php
use App\Filament\Student\Resources\ResultResource;
use App\Models\Assessment;
use App\Enums\InvoiceStatus;

$student = $getRecord();

$hasUnpaidPortalFee = $student->registrations()
        ->whereDoesntHave('portalInvoice', function ($query) {
            $query->where('invoice_status', InvoiceStatus::PAID);
        })
        ->exists();

$assessmentNames = Assessment::pluck('max_score', 'name')->all();

$courseData = ResultResource::getSemesterCourseData($this, $student) ?? [];

// Semester calculations
$semesterTotalCreditUnit = ResultResource::getSemesterTotalCreditUnit($courseData);
$semesterTotalGradePoint = ResultResource::getSemesterTotalGradePoint($courseData);
$semesterGradePointAverage = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit, 2) : null;
$semesterGradeRemark = ResultResource::getRemarkFromGradePointAverage($semesterGradePointAverage);
$semesterOutstandingCourses = ResultResource::getSemesterOutstandingCourses($courseData);

// Cumulative calculations
$cumulativeTotalCreditUnit = ResultResource::getCumulativeTotalCreditUnit($this, $student);
$cumulativeTotalGradePoint = ResultResource::getCumulativeTotalGradePoint($this, $student);
$cumulativeGradePointAverage = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint / $cumulativeTotalCreditUnit, 2) : null;
$cumulativeGradeRemark = ResultResource::getRemarkFromGradePointAverage($cumulativeGradePointAverage);
$cumulativeOutstandingCourses = ResultResource::getCumulativeOutstandingCourses($this, $student);
@endphp

<div class="my-3 py-3 inline-block rounded-sm overflow-hidden border border-gray-300">
    <div class="inline-block px-4 text-center">

        <h2 class="text-lg font-semibold mb-2 text-center">Result Sheet</h2>
       
        <table class="table-auto text-sm border border-gray-300">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Course</th>
                    @foreach ($assessmentNames as $assessment => $max_score)
                        <th class="border px-2 py-1 text-center">
                            {{ $assessment }}<br>
                            <span class="block text-xs">({{ $max_score }})</span>
                        </th>
                    @endforeach
                    <th class="border px-2 py-1 text-center">Total<br><span class="block text-xs">({{ collect($assessmentNames)->sum() }})</span></th>
                    <th class="border px-2 py-1 text-center">Grade</th>
                    <th class="border px-2 py-1 text-center">Point</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($courseData as $index => $data)
                    @php $failed = ($data['total_score'] ?? -1) <= ResultResource::getFailedScore(); @endphp
                    <tr>
                        <td class="border px-2 py-1 text-center">{{ $index + 1 }}</td>
                        <td class="border px-2 py-1" x-tooltip.raw="{{ $data['title'] }}">
                            {{ $data['code'] }}
                            <span class="text-gray-400"> {{ $data['credit'] }}{{ $data['status'] }}</span>
                        </td>
                        @foreach ($assessmentNames as $assessment => $max_score)
                            <td class="border px-2 py-1 text-center">{{ $data[$assessment] ?? '-' }}</td>
                        @endforeach
                        <td class="border px-2 py-1 text-center text-{{ $failed ? 'red-500' : 'gray-900'}}">
                            {{ $data['total_score'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1 text-center text-{{ $failed ? 'red-500' : 'gray-900' }}">
                            {{ $data['grade'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1 text-center">{{ $data['point'] ?? '-' }}</td>
                        <td class="border px-2 py-1 text-center">{{ $data['grade_point'] ?? '-' }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="{{ 6 + count($assessmentNames) }}" class="text-center py-2">No scores found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <div class="mt-3 w-full max-w-md text-sm">
            <h2 class="text-lg font-semibold text-center mb-2">Result Summary</h2>

            <h3 class="font-semibold text-center mb-2">Semester</h3>
            <table class="table-auto border border-gray-300 w-full text-center">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-3 py-2" x-tooltip.raw="Total Credit Unit">TCU</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Total Grade Point">TGP</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Grade Point Average">GPA</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Remark">Remark</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Outstanding Courses">Outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border px-3 py-2">{{ $semesterTotalCreditUnit }}</td>
                        <td class="border px-3 py-2">{{ $semesterTotalGradePoint }}</td>
                        <td class="border px-3 py-2">{{ $semesterGradePointAverage ?? '-' }}</td>
                        <td class="border px-3 py-2">{{ $semesterGradeRemark?->remark ?? '-' }}</td>
                        <td class="border px-3 py-2 text-start max-w-[150px]">
                            <div class="overflow-auto max-h-[100px]">
                                {{ $semesterOutstandingCourses->isNotEmpty() ? $semesterOutstandingCourses->pluck('code')->implode(', ') : 'NIL' }}
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <h3 class="font-semibold text-center my-2">Cumulative</h3>
            <table class="table-auto border border-gray-300 w-full text-center">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Total Credit Unit">CTCU</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Total Grade Point">CTGP</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative remark">C. remark</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative outstanding courses">C. outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    @if($hasUnpaidPortalFee)
                    <tr>
                        <td colspan="5" class="border px-3 py-2 text-center">You have unpaid portal fees. Please pay now to get full access.</td>
                    </tr>
                    @else                   
                    <tr>
                        <td class="border px-3 py-2">{{ $cumulativeTotalCreditUnit }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeTotalGradePoint }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeGradePointAverage ?? '-' }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeGradeRemark?->remark ?? '-' }}</td>
                        <td class="border px-3 py-2 text-start max-w-[150px]">
                            <div class="overflow-auto max-h-[100px]">
                                {{ $cumulativeOutstandingCourses->isNotEmpty() ? $cumulativeOutstandingCourses->pluck('code')->implode(', ') : 'NIL' }}
                            </div>
                        </td>
                        
                    </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
