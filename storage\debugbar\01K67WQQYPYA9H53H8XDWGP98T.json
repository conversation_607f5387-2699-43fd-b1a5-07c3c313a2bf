{"__meta": {"id": "01K67WQQYPYA9H53H8XDWGP98T", "datetime": "2025-09-28 11:08:47", "utime": **********.062812, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759054125.986385, "end": **********.062839, "duration": 1.0764539241790771, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1759054125.986385, "relative_start": 0, "end": **********.805478, "relative_end": **********.805478, "duration": 0.****************, "duration_str": "819ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.805499, "relative_start": 0.****************, "end": **********.062842, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.841814, "relative_start": 0.**************, "end": **********.844157, "relative_end": **********.844157, "duration": 0.0023429393768310547, "duration_str": "2.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.tables.registrations", "start": **********.963893, "relative_start": 0.****************, "end": **********.963893, "relative_end": **********.963893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.976777, "relative_start": 0.****************, "end": **********.976777, "relative_end": **********.976777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.993039, "relative_start": 1.****************, "end": **********.993039, "relative_end": **********.993039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.004171, "relative_start": 1.0177857875823975, "end": **********.004171, "relative_end": **********.004171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.registration-toggle", "start": **********.018341, "relative_start": 1.0319559574127197, "end": **********.018341, "relative_end": **********.018341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.036291, "relative_start": 1.049905776977539, "end": **********.036291, "relative_end": **********.036291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.050002, "relative_start": 1.0636169910430908, "end": **********.050002, "relative_end": **********.050002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.060485, "relative_start": 1.0740997791290283, "end": **********.061456, "relative_end": **********.061456, "duration": 0.0009710788726806641, "duration_str": "971μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7105952, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "filament.tables.registrations", "param_count": null, "params": [], "start": **********.963821, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/registrations.blade.phpfilament.tables.registrations", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fregistrations.blade.php&line=1", "ajax": false, "filename": "registrations.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.976709, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.992973, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.004086, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "livewire.registration-toggle", "param_count": null, "params": [], "start": **********.018206, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/registration-toggle.blade.phplivewire.registration-toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fregistration-toggle.blade.php&line=1", "ajax": false, "filename": "registration-toggle.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.036208, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.049943, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}]}, "queries": {"count": 13, "nb_statements": 12, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016290000000000002, "accumulated_duration_str": "16.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.859511, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'YgLUBVXM1mKBnbJvS3aIq4RkRpXoL5gRfsghIL0m' limit 1", "type": "query", "params": [], "bindings": ["YgLUBVXM1mKBnbJvS3aIq4RkRpXoL5gRfsghIL0m"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.862689, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 24.002}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.893512, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 24.002, "width_percent": 6.262}, {"sql": "select * from `users` where `users`.`id` = 22 and `users`.`role` = 1 and `users`.`deleted_at` is null order by `users`.`id` asc limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.929852, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 30.264, "width_percent": 4.727}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.934575, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 34.991, "width_percent": 7.551}, {"sql": "select * from `programmes` where `programmes`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.9395082, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 42.541, "width_percent": 4.972}, {"sql": "select * from `levels` where `levels`.`id` in (1, 2) and `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.942947, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 47.514, "width_percent": 4.788}, {"sql": "select * from `semesters` where `semesters`.`id` in (1, 2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.946342, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 52.302, "width_percent": 4.604}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` in (2, 3) and `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.94995, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 56.906, "width_percent": 4.85}, {"sql": "select * from `invoices` where `fee_type` = 1 and `invoices`.`payable_id` in (20, 35, 36, 185) and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 27, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.955343, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 61.756, "width_percent": 10.62}, {"sql": "select * from `applications` where `applications`.`user_id` = 22 and `applications`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 378}, {"index": 22, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 10}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.040608, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "User.php:378", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 378}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=378", "ajax": false, "filename": "User.php", "line": "378"}, "connection": "racoed", "explain": null, "start_percent": 72.376, "width_percent": 11.05}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 386}, {"index": 15, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.045912, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:386", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=386", "ajax": false, "filename": "User.php", "line": "386"}, "connection": "racoed", "explain": null, "start_percent": 83.425, "width_percent": 6.139}, {"sql": "select * from `guardians` where `guardians`.`user_id` = 22 and `guardians`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": "view", "name": "filament.hooks.global-bio-data-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.php", "line": 13}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.0528412, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "User.php:344", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=344", "ajax": false, "filename": "User.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 89.564, "width_percent": 10.436}]}, "models": {"data": {"App\\Models\\Registration": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Level": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\Semester": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\Programme": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=1", "ajax": false, "filename": "Programme.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\Guardian": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGuardian.php&line=1", "ajax": false, "filename": "Guardian.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.student.resources.registration-resource.pages.manage-registrations #sxTpBH4T4cCQZiXUC70l": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => null\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.student.resources.registration-resource.pages.manage-registrations\"\n  \"component\" => \"App\\Filament\\Student\\Resources\\RegistrationResource\\Pages\\ManageRegistrations\"\n  \"id\" => \"sxTpBH4T4cCQZiXUC70l\"\n]", "registration-toggle #ZZyTZpCIHDPNgb9E7knv": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2400\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 20\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 1\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 1\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 19:13:49\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 20\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 1\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 1\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 19:13:49\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2344\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2310\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2293\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2272\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => null\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => true\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"ZZyTZpCIHDPNgb9E7knv\"\n]", "registration-toggle #j5JFNoHQxxyJN9e0ML6z": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2366\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 35\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 1\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:35\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 35\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 1\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:35\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2344\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2308\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2293\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"First semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2268\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => App\\Models\\Invoice {#2249\n          #connection: \"mysql\"\n          #table: \"invoices\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 80\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 35\n            \"number\" => \"CPAY22072025081\"\n            \"reference\" => \"REF20250722203536-687FF61801776\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:16 [\n            \"id\" => 80\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 35\n            \"number\" => \"CPAY22072025081\"\n            \"reference\" => \"REF20250722203536-687FF61801776\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:9 [\n            \"total_amount\" => \"App\\Casts\\MoneyCast\"\n            \"invoice_status\" => \"App\\Enums\\InvoiceStatus\"\n            \"fee_type\" => \"App\\Enums\\FeeType\"\n            \"expired_at\" => \"datetime\"\n            \"paid_at\" => \"datetime\"\n            \"description\" => \"array\"\n            \"metadata\" => \"array\"\n            \"payment_success\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => false\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"j5JFNoHQxxyJN9e0ML6z\"\n]", "registration-toggle #pdtwCmRcNr3ZmF0EEDko": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2401\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 36\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 2\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:36\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 36\n        \"user_id\" => 22\n        \"school_session_id\" => 2\n        \"semester_id\" => 2\n        \"level_id\" => 1\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 0\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-07-22 20:35:36\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2344\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2308\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 1\n            \"name\" => \"NCE 1\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2295\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2268\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 2\n            \"name\" => \"2023/2024\"\n            \"is_active\" => 0\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => App\\Models\\Invoice {#2251\n          #connection: \"mysql\"\n          #table: \"invoices\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 81\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 36\n            \"number\" => \"CPAY22072025082\"\n            \"reference\" => \"REF20250722203536-687FF61805037\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:16 [\n            \"id\" => 81\n            \"user_id\" => 22\n            \"payable_type\" => \"App\\Models\\Registration\"\n            \"payable_id\" => 36\n            \"number\" => \"CPAY22072025082\"\n            \"reference\" => \"REF20250722203536-687FF61805037\"\n            \"description\" => \"[{\"item\": \"Portal access\", \"amount\": 3000}]\"\n            \"total_amount\" => 300000\n            \"fee_type\" => 1\n            \"invoice_status\" => 3\n            \"payment_success\" => 0\n            \"paid_at\" => \"2025-07-22 20:35:36\"\n            \"metadata\" => null\n            \"created_at\" => \"2025-07-22 20:35:36\"\n            \"updated_at\" => \"2025-07-22 20:35:36\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:9 [\n            \"total_amount\" => \"App\\Casts\\MoneyCast\"\n            \"invoice_status\" => \"App\\Enums\\InvoiceStatus\"\n            \"fee_type\" => \"App\\Enums\\FeeType\"\n            \"expired_at\" => \"datetime\"\n            \"paid_at\" => \"datetime\"\n            \"description\" => \"array\"\n            \"metadata\" => \"array\"\n            \"payment_success\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => false\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"pdtwCmRcNr3ZmF0EEDko\"\n]", "registration-toggle #gjsqeDMkQECP4BkCKZ0J": "array:4 [\n  \"data\" => array:4 [\n    \"registration\" => App\\Models\\Registration {#2337\n      #connection: \"mysql\"\n      #table: \"registrations\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: true\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:11 [\n        \"id\" => 185\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 2\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-09-15 19:59:03\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #original: array:11 [\n        \"id\" => 185\n        \"user_id\" => 22\n        \"school_session_id\" => 3\n        \"semester_id\" => 2\n        \"level_id\" => 2\n        \"programme_id\" => 1\n        \"is_active\" => 0\n        \"is_graduated\" => 1\n        \"is_withdrawn\" => 0\n        \"created_at\" => \"2025-09-15 19:59:03\"\n        \"updated_at\" => \"2025-09-16 22:09:12\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"is_active\" => \"boolean\"\n        \"is_graduated\" => \"boolean\"\n        \"is_withdrawn\" => \"boolean\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:5 [\n        \"programme\" => App\\Models\\Programme {#2344\n          #connection: \"mysql\"\n          #table: \"programmes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"name\" => \"Business Education (DM)\"\n            \"code\" => \"526243E\"\n            \"first_department_id\" => 16\n            \"second_department_id\" => null\n            \"school_id\" => 6\n            \"is_active\" => 1\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"is_active\" => \"boolean\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"level\" => App\\Models\\Level {#2310\n          #connection: \"mysql\"\n          #table: \"levels\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"NCE 2\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"semester\" => App\\Models\\Semester {#2295\n          #connection: \"mysql\"\n          #table: \"semesters\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #original: array:5 [\n            \"id\" => 2\n            \"name\" => \"Second semester\"\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-07-07 11:10:30\"\n            \"updated_at\" => \"2025-07-07 11:10:30\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"schoolSession\" => App\\Models\\SchoolSession {#2272\n          #connection: \"mysql\"\n          #table: \"school_sessions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: true\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #original: array:6 [\n            \"id\" => 3\n            \"name\" => \"2024/2025\"\n            \"is_active\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-09-15 10:49:23\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:2 [\n            \"is_active\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        \"portalInvoice\" => null\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"isActive\" => false\n    \"userId\" => 22\n    \"isDisabled\" => true\n  ]\n  \"name\" => \"registration-toggle\"\n  \"component\" => \"App\\Livewire\\RegistrationToggle\"\n  \"id\" => \"gjsqeDMkQECP4BkCKZ0J\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Student\\Resources\\RegistrationResource\\Pages\\ManageRegistrations@loadTable<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/CanDeferLoading.php:17-20</a>", "middleware": "web", "duration": "1.07s", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1383179399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1383179399\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-860977029 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W2sMx0HnZklv4yF1TeIG3UoZHX6OQl78vWacmapj</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1656 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:null,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:null,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;sxTpBH4T4cCQZiXUC70l&quot;,&quot;name&quot;:&quot;app.filament.student.resources.registration-resource.pages.manage-registrations&quot;,&quot;path&quot;:&quot;student\\/registrations&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;1f9c7c945a21cfee4778fccb387340ab3165a6cfd2c1f219fc115af8a93a59c3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">loadTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860977029\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1273354923 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6Ijh0bmNDWEMxL0dWR2RkYkM5WURyUXc9PSIsInZhbHVlIjoiUGVsQWJuVlZDUUJXS0ZZVDFjT011RzJ6d3g1SGhhUXZ1WHZyRUdyR0lTeUxjdy9lbFFMWDRXcEVtaFhGRlRKTlE2QjZxelhYb1NLdHBIWHJJUENRQVNsQ3hmZk9zSVNPMlQyV0N5UkYwOVhmNmhvalVjbTZWdXFWbXJyZzJXT2MiLCJtYWMiOiI1MmUzNGU1M2M5ZjM1Mzc0OWRhY2Q4ZTY0YWVkZjY4NDA5MDBmZjYyZGE4YTM3YWViMzZlMGM5YjExOGNhNGM3IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6ImtFTE41eEwzR3BNVXVVa1FjKzNXZ3c9PSIsInZhbHVlIjoiQlBGc3dhaWpScGJoL1pJY3VET0h1bStpQjZINjA0S2dBY2FHaDhDeU13dGtsQkYrOFV6ejQyQnlTNUR0NnRqZkxJK1FXc0tUYmdKNVZMMmlIRmtqeDZ5T3lCR0ZGSUgyZjNWWHR4R0JSb2NzcEJTMU53TGR1K044TmFQdmRKZEQiLCJtYWMiOiIwNzM2ZmU2NDkxZDY3NTAyY2M4NDQ1MzM0ZTQ1NzkzOWM3OGQzYzU0Njg3OTkwNmU0NDk2MjVlMGI0ZDYyY2NkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">https://portal.racoed.test/student/registrations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1997</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273354923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1002468073 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W2sMx0HnZklv4yF1TeIG3UoZHX6OQl78vWacmapj</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YgLUBVXM1mKBnbJvS3aIq4RkRpXoL5gRfsghIL0m</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002468073\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-833334700 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 28 Sep 2025 10:08:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833334700\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1185360241 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W2sMx0HnZklv4yF1TeIG3UoZHX6OQl78vWacmapj</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"48 characters\">https://portal.racoed.test/student/registrations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">https://portal.racoed.test/registration/print/20?signature=8b1aaca542907dc5f52d40838e9eeef4b41f8c929175bed83d854d5aa81a9298</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageRegistrations_filters</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185360241\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}