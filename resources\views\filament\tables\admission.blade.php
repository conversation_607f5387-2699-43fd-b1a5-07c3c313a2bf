@php
use App\Enums\AdmissionStatus;
use App\Settings\AdmissionSettings;
use App\Filament\Student\Clusters\Cpay\Resources\PortalResource;
use App\Enums\InvoiceStatus;

$student = $getRecord();

$hasUnpaidPortalFee = $student->registrations()
        ->whereDoesntHave('portalInvoice', function ($query) {
            $query->where('invoice_status', InvoiceStatus::PAID);
        })
        ->exists();

$admissionSettings = app(AdmissionSettings::class);
@endphp

<div class="flex flex-col sm:flex-row gap-8 sm:gap-4">

    <!-- Left div  -->
    <div class="order-2 sm:order-1 flex-1 max-w-lg space-y-4">

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Application number:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $student->application->number ?? 'NIL'}}
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Entry Programme:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $student->application->programme->name ?? 'NIL' }}
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Screening score:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $student->application->screening_score ?? 'NIL' }}
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Screening status:
                </span>
                <span class="text-gray-900 text-sm">
                    @if ($student->application->screening_status)
                    <x-filament::badge :color="$student->application->screening_status->getColor()">
                        {{ $student->application->screening_status->getLabel() }}
                    </x-filament::badge>
                    @else
                    NIL
                    @endif

                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Admission status:
                </span>
                <span class="text-gray-900 text-sm">
                    @if($student->application->admission_status)
                    <x-filament::badge :color="$student->application->admission_status->getColor()">
                        {{ $student->application->admission_status->getLabel() }}
                    </x-filament::badge>
                    @else
                    NIL
                    @endif
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Admission date:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $student->application->admission_date ? \Carbon\Carbon::parse($student->application->admission_date)->format('d M, Y') : 'NIL' }}
                </span>                
            </div>
        </div>
    </div>

    <!-- Right div  -->
    @if($hasUnpaidPortalFee)
          <div class="flex-1 bg-amber-100 rounded-sm shadow-sm border border-amber-400 text-amber-800 p-4">
            <div class=" flex items-center justify-center pb-2">
                <x-filament::icon icon="heroicon-s-exclamation-circle" class="w-6 h-6 text-amber-600"/>
            </div>
            <h2 class="font-bold text-center">You have unpaid portal fees.
            </h2>
            <p class="text-gray-900 text-center text-sm">Please pay now to get full access to your portal.</p>
             <a href="{{ PortalResource::getUrl('index') }}" class="text-blue-600 underline block text-center mt-2">View portal fees</a>
        </div>
    
    @elseif ($student->registrations->contains('is_graduated', true))
      <div class="flex-1 bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class=" flex items-center justify-center pb-2">
                <x-filament::icon icon="heroicon-o-academic-cap" class="w-6 h-6 text-green-500"/>
            </div>
            <h2 class="font-bold text-center text-gray-700">GRADUATED!!!
            </h2>
          <p class="text-gray-900 text-center text-sm">
                Congratulations <span class="font-semibold">{{ $student->name }}</span> 
                (Matric: {{ $student->matric_number }}) on your graduation from our college.  
                You have fulfilled all NCE academic requirements.<br> 
                We extend our best wishes for your future endeavors.
                </p>
        </div>
    @elseif ($student->application->admission_status === AdmissionStatus::PENDING)

        <div class="flex-1 bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class=" flex items-center justify-center pb-2">
                <x-filament::icon icon="heroicon-s-clock" class="w-6 h-6 text-gray-500" />
            </div>
            <h2 class="font-bold text-center text-gray-700">ADMISSION IN PROGRESS...
            </h2>
            <p class="text-gray-900 text-center text-sm">Dear applicant, your admission is currently under
                processing.<br>Thank you for your patience. </p>
                {{-- APPLICATION DATA --}}
                <div class="flex justify-center gap-4" style="margin-top: 1rem">
                    <x-filament::dropdown>
                        <x-slot name="trigger">
                            <x-filament::button tooltip="Export application data">
                                Application data
                            </x-filament::button>
                        </x-slot>
                        <x-filament::dropdown.list>
                            <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printApplicationData('{{ $student->id }}')">
                                    Print
                            </x-filament::dropdown.list.item>
                            <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadApplicationData('{{ $student->id }}')">
                                    Download
                            </x-filament::dropdown.list.item>
                        </x-filament::dropdown.list>
                    </x-filament::dropdown>
                </div>

        </div>

    @elseif ($student->application->admission_status === AdmissionStatus::APPROVED)
        <div class="flex-1 bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class=" flex items-center justify-center pb-2">
                <x-filament::icon icon="heroicon-s-check-circle" class="w-6 h-6 text-green-500"/>
            </div>
            <h2 class="font-bold text-center text-gray-700">ADMISSION APPROVED!!!
            </h2>
            <p class="text-gray-900 text-center text-sm">Congratulations! You have been offered admission into our college.<br> We are excited
                to see all you will achieve.<br> Welcome aboard! </p>

            {{-- APPLICATION DATA --}}
            <div class="flex justify-center gap-4" style="margin-top: 1rem">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button tooltip="Export application data">
                            Application data
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printApplicationData({{ $student->id }})">                              
                                    Print                              
                        </x-filament::dropdown.list.item>
                        <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadApplicationData({{ $student->id }})">                              
                                Download                                
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>
            
            {{-- ADMISSION LETTER --}}
            <div class="flex justify-center gap-4" style="margin-top: 1rem">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button tooltip="Export admission letter">
                            Admission letter
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printAdmissionLetter({{ $student->id }})">
                        Print
                        </x-filament::dropdown.list.item>
                        <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadAdmissionLetter({{ $student->id }})">
                            Download
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>

            {{-- ACCEPTANCE LETTER --}}
            <div class="flex justify-center gap-4" style="margin-top: 1rem">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button tooltip="Export acceptance letter">
                            Acceptance letter
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printAcceptanceLetter({{ $student->id }})">
                        Print
                        </x-filament::dropdown.list.item>
                        <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadAcceptanceLetter({{ $student->id }})">
                            Download
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>

            {{-- FEE SCHEDULE --}}  
            <div class="flex justify-center gap-4" style="margin-top: 1rem">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button tooltip="Export fee schedule">
                            Fee schedule
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        @if($admissionSettings->fee_schedule)
                        <x-filament::dropdown.list.item 
                            icon="heroicon-s-document-arrow-down"
                            tag="a"
                            href="{{ Storage::url($admissionSettings->fee_schedule) }}"
                            target="_blank"
                            download="{{ basename($admissionSettings->fee_schedule) }}">
                            Download
                        </x-filament::dropdown.list.item>
                        @else
                        <x-filament::dropdown.list.item disabled>
                            No file available
                        </x-filament::dropdown.list.item>
                    @endif
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>

        </div>

    @elseif ($student->application->admission_status === AdmissionStatus::DENIED)
        <div class="flex-1 bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class=" flex items-center justify-center pb-2">
                <x-filament::icon icon="heroicon-s-exclamation-circle" class="w-6 h-6 text-red-500" />
            </div>
            <h2 class="font-bold text-center text-gray-700">ADMISSION DENIED!!!
            </h2>
            <p class="text-gray-900 text-center text-sm">Dear applicant, we regret to inform you that your admission has
                been denied.<br>Thank you for your interest, and we wish you the best in your future endeavors. </p>
        </div>
    @endif

</div>
