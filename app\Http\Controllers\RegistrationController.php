<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Department;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Semester;
use App\Models\Level;
use App\Settings\CollegeSettings;
use Spa<PERSON>\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Str;

class RegistrationController extends Controller
{

    public function print(Registration $registration)
    {
        return view('filament.documents.registration', $this->getRegistrationData($registration));
    }

    public function download(Registration $registration)
    {
        return Pdf::view('filament.documents.registration', $this->getRegistrationData($registration))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name($this->getRegistrationData($registration)['fileName'] . '.pdf')
            ->download();
    }

    private function getRegistrationData(Registration $registration)
    {
        $collegeSettings = app(CollegeSettings::class);
        $student = $registration->user;

        $session = SchoolSession::find($registration->school_session_id)?->name;
        $semester = Semester::find($registration->semester_id)?->name;
        $level = Level::find($registration->level_id)?->name;
        $department = Department::find($registration->programme->first_department_id)?->name;

        $fileName = 'Registration - ' . Str::slug(Str::replace('/', '-', implode(' ', [
            $student->name,
            $session,
            $semester,
            $level,
            $department,
        ])));

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
            'registration' => $registration,
            'coursesByDepartment' => $this->getCoursesByDepartment($registration),
            'departmentsWithHeads' => $this->getDepartmentsWithHeads($registration),
            'fileName' => $fileName
        ];
    }

    private function getCoursesByDepartment(Registration $registration)
    {
        $levelId = $registration->level_id;
        $semesterId = $registration->semester_id;

        $firstDeptId = $registration->programme->first_department_id;
        $secondDeptId = $registration->programme->second_department_id;

        $educationDeptId = Department::where('is_edu', true)->value('id');
        $gseDeptId = Department::where('is_gse', true)->value('id');

        $allDeptIds = array_filter([$educationDeptId, $gseDeptId, $firstDeptId, $secondDeptId]);

        $courses = Course::whereIn('department_id', $allDeptIds)
            ->where('level_id', $levelId)
            ->where('semester_id', $semesterId)
            ->get()
            ->groupBy('department_id');

        $result = [];
        foreach ($allDeptIds as $deptId) {
            $dept = Department::find($deptId);
            if ($dept) {
                $result[] = [
                    'department' => $dept,
                    'courses' => $courses->get($deptId, collect())
                ];
            }
        }

        return $result;
    }

    private function getDepartmentsWithHeads(Registration $registration)
    {
        $firstDeptId = $registration->programme->first_department_id;
        $secondDeptId = $registration->programme->second_department_id;

        $educationDeptId = Department::where('is_edu', true)->value('id');
        $gseDeptId = Department::where('is_gse', true)->value('id');

        $allDeptIds = array_filter([$educationDeptId, $gseDeptId, $firstDeptId, $secondDeptId]);

        $departments = [];
        foreach ($allDeptIds as $deptId) {
            $dept = Department::with('headOfDepartment')->find($deptId);
            if ($dept) {
                $departments[] = $dept;
            }
        }

        return $departments;
    }
}
