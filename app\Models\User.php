<?php

namespace App\Models;

use App\Enums\Role;
use Filament\Panel;
use App\Enums\Title;
use App\Enums\AddressState;
use Illuminate\Support\Str;
use App\Enums\InvoiceStatus;
use App\Enums\AdmissionStatus;
use Filament\Models\Contracts\HasName;
// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Storage;
use Filament\Models\Contracts\HasAvatar;
use Illuminate\Notifications\Notifiable;
use Filament\Models\Contracts\FilamentUser;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;


class User extends Authenticatable implements FilamentUser, HasName, HasAvatar
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role' => Role::class,
            'date_of_birth' => 'datetime',
            'title' => Title::class,
            'qualification' => 'array',
            'address_state' => AddressState::class,
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return match ($panel->getId()) {
            'auth' => in_array($this->role, array_merge(all_staff_roles(), [Role::STUDENT])),
            'staff', 'help' => in_array($this->role, all_staff_roles()),
            'student' => in_array($this->role, [
                Role::STUDENT,
            ]),
            default => false,
        };
    }
    //Get attributes
    public function name(): Attribute
    {
        return Attribute::get(fn() => $this->getFilamentName());
    }

    public function gender(): Attribute
    {
        return Attribute::get(fn($value) => Str::ucfirst($value));
    }

     public function getFilamentName(): string
    {
        return collect([$this->last_name, $this->first_name, $this->middle_name])
            ->filter()
            ->values()
            ->map(fn($part, $key) => $key === 0 
                ? Str::upper($part) 
                : Str::ucfirst(Str::lower($part))
            )
            ->join(' ');
    }
    // NOTE: giving page not found
    // public function getRouteKeyName()
    // {
    //     return 'phone';
    // }

    public function getFilamentAvatarUrl(): ?string
    {
        return $this->photo ? Storage::url($this->photo) : null;
    }

    public function address(): Attribute
    {
        return Attribute::get(function () {
            $state = $this->address_state ? $this->address_state->getLabel() . ' State' : null;

            $parts = array_filter([$this->address_line, $this->address_town, $state]);
            $address = implode(', ', $parts);

            return $address ? Str::title($address) : null;
        });
    }
    
    public function religion(): Attribute
    {
        return Attribute::get(fn($value) => Str::ucfirst($value));
    }

    public function nationality(): Attribute
    {
        return Attribute::get(fn($value) => Str::ucfirst($value));
    }

    public function maritalStatus(): Attribute
    {
        return Attribute::get(fn($value) => Str::ucfirst($value));
    }
    //Relationship
    /**
     * Get all of the invoices for the subscriber
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the state that owns the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the local government area that owns the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function localGovernmentArea(): BelongsTo
    {
        return $this->belongsTo(LocalGovernmentArea::class);
    }

    /**
     * Get the application that owns the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function application(): HasOne
    {
        return $this->hasOne(Application::class);
    }

    /**
     * Get the guardian that owns the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function guardian(): HasOne
    {
        return $this->hasOne(Guardian::class);
    }

    /**
     * Get the registrations for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function registrations(): HasMany
    {
        return $this->hasMany(Registration::class);
    }

    /**
     * Get the active registration for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function activeRegistration(): HasOne
    {
        return $this->hasOne(Registration::class)->where('is_active', true);
    }

    /**
     * Get the active school session for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function activeSchoolSession(): HasOneThrough
    {
        return $this->hasOneThrough(SchoolSession::class, Registration::class, 'user_id', 'id', 'id', 'school_session_id')
            ->where('registrations.is_active', true);
    }

    /**
     * Get the  school sessions for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function schoolSessions(): HasManyThrough
    {
        return $this->hasManyThrough(SchoolSession::class, Registration::class, 'user_id', 'id', 'id', 'school_session_id');
    }

    /**
     * Get the active semester for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function activeSemester(): HasOneThrough
    {
        return $this->hasOneThrough(Semester::class, Registration::class, 'user_id', 'id', 'id', 'semester_id')
            ->where('registrations.is_active', true);
    }

    /**
     * Get the semesters for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function semesters(): HasManyThrough
    {
        return $this->hasManyThrough(Semester::class, Registration::class, 'user_id', 'id', 'id', 'semester_id');
    }

    /**
     * Get the active level for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function activeLevel(): HasOneThrough
    {
        return $this->hasOneThrough(Level::class, Registration::class, 'user_id', 'id', 'id', 'level_id')
            ->where('registrations.is_active', true);
    }

    /**
     * Get the levels for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function levels(): HasManyThrough
    {
        return $this->hasManyThrough(Level::class, Registration::class, 'user_id', 'id', 'id', 'level_id');
    }

    /**
     * Get the active department for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function departments(): HasMAnyThrough
    {
        return $this->hasManyThrough(Department::class, Registration::class, 'user_id', 'id', 'id', 'department_id');
    }

    /**
     * Get the active programme for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function activeProgramme(): HasOneThrough
    {
        return $this->hasOneThrough(Programme::class, Registration::class, 'user_id', 'id', 'id', 'programme_id')
            ->where('registrations.is_active', true);
    }

    /**
     * Get the department that owns the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the total scores for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function totalScores(): HasMany
    {
        return $this->hasMany(TotalScore::class);
    }

    /**
     * Check if the user's biodata is complete
     *
     * @return bool
     */
    public function isBiodataComplete(): bool
    {
        if ($this->role === Role::STUDENT) {
            return $this->isStudentBiodataComplete();
        }

        return $this->isStaffBiodataComplete();
    }

    protected function isStudentBiodataComplete(): bool
    {
        return $this->first_name
            && $this->last_name
            && $this->address_line
            && $this->address_town
            && $this->address_state
            && $this->date_of_birth
            && $this->gender
            && $this->marital_status
            && $this->religion
            && $this->nationality
            && $this->state_id
            && $this->local_government_area_id
            && $this->photo
            && $this->application
            && $this->application?->programme_id
            && $this->application?->school_session_id
            && $this->application?->secondary_school_attended
            && $this->application?->secondary_school_graduation_year
            && $this->application?->exam_board
            && $this->application?->exam_result
            && $this->guardian
            && $this->guardian?->relationship
            && $this->guardian?->occupation
            && $this->guardian?->phone
            && $this->guardian?->first_name
            && $this->guardian?->last_name
            && $this->guardian?->title;
    }

    protected function isStaffBiodataComplete(): bool
    {
        return $this->first_name
            && $this->last_name
            && $this->address_line
            && $this->address_town
            && $this->address_state
            && $this->date_of_birth
            && $this->gender
            && $this->marital_status
            && $this->religion
            && $this->photo;
    }

    /**
     * Check if student has unpaid portal fees
     *
     * @return boolean
     */
    public function hasUnpaidPortalFee(): bool
    {
        if ($this->role !== Role::STUDENT) {
            return false;
        }

        if ($this->application?->admission_status !== AdmissionStatus::APPROVED) {
            return false;
        }

        return $this->registrations()
            ->whereDoesntHave('portalInvoice', fn ($q) =>
                $q->where('invoice_status', InvoiceStatus::PAID)
            )
            ->exists();
    }

}
