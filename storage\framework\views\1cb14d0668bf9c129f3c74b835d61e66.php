<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="<?php echo e(asset('images/racoed-favicon.png')); ?>" type="image/png">   
    <title>Registration - <?php echo e($student->name); ?></title>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo \Filament\Support\Facades\FilamentAsset::renderStyles() ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/filament/custom/theme.css', 'resources/css/app.css']); ?>
    <style>
           @media print {
            @page { size: A4; margin: 0mm; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
            .print-grid {
                    display: grid !important;
                    grid-template-columns: 1fr 1fr !important;
                    gap: 1rem;
                }
                th, tfoot tr {
                  background-color: #f9fafb !important; /* Tailwind's gray-50 */
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
              }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        
        <?php if (isset($component)) { $__componentOriginal748b58a65cd16e6aeabed19711d43de4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal748b58a65cd16e6aeabed19711d43de4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.document-header','data' => ['collegeLogo' => asset('images/racoed-favicon.png'),'collegeName' => $collegeSettings->name,'collegeMotto' => $collegeSettings->motto,'collegeAddress' => $collegeSettings->address,'collegePhone' => $collegeSettings->phone,'collegeEmail' => $collegeSettings->email,'studentPhoto' => $student->photo ? Storage::url($student->photo) : asset('images/placeholder.png'),'heading' => 'Examination & Records','subheading' => 'Course Registration']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('document-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['collegeLogo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(asset('images/racoed-favicon.png')),'collegeName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->name),'collegeMotto' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->motto),'collegeAddress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->address),'collegePhone' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->phone),'collegeEmail' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->email),'studentPhoto' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')),'heading' => 'Examination & Records','subheading' => 'Course Registration']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal748b58a65cd16e6aeabed19711d43de4)): ?>
<?php $attributes = $__attributesOriginal748b58a65cd16e6aeabed19711d43de4; ?>
<?php unset($__attributesOriginal748b58a65cd16e6aeabed19711d43de4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal748b58a65cd16e6aeabed19711d43de4)): ?>
<?php $component = $__componentOriginal748b58a65cd16e6aeabed19711d43de4; ?>
<?php unset($__componentOriginal748b58a65cd16e6aeabed19711d43de4); ?>
<?php endif; ?> 

        
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Student & Academic Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-1">
                <div class="border p-0.5"><strong>Name:</strong> <?php echo e($student->name ?? 'NIL'); ?></div>
                <div class="border p-0.5"><strong>Session:</strong> <?php echo e($registration?->schoolSession->name ?? 'NIL'); ?></div> 
                <div class="border p-0.5 "><strong>Matric. no.:</strong> <?php echo e($student->matric_number ?? 'NIL'); ?></div>
                <div class="border p-0.5"><strong>Level:</strong> <?php echo e($registration?->level->name ?? 'NIL'); ?></div>
                <div class="border p-0.5"><strong>Phone:</strong> <?php echo e($student->phone ?? 'NIL'); ?></div>   
                <div class="border p-0.5"><strong>Semester:</strong> <?php echo e($registration?->semester->name ?? 'NIL'); ?></div>
                <div class="border p-0.5"><strong>Gender:</strong> <?php echo e($student->gender ?? 'NIL'); ?></div> 
                <div class="border p-0.5"><strong>Programme:</strong> <?php echo e($registration?->programme->name ?? 'NIL'); ?></div>                   
            </div>
        </div>

      
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Registered Courses</h2>  

            <div class="grid grid-cols-1 md:grid-cols-2 gap-1 text-xs print-grid">
                <?php
                    $levelId = $registration->level_id;
                    $semesterId = $registration->semester_id;

                    $firstDeptId = $registration->programme->first_department_id;
                    $secondDeptId = $registration->programme->second_department_id;

                    $educationDeptId = \App\Models\Department::where('is_edu', true)->value('id');
                    $gseDeptId = \App\Models\Department::where('is_gse', true)->value('id');

                    $allDeptIds = [$educationDeptId, $gseDeptId, $firstDeptId, $secondDeptId];

                    $courses = \App\Models\Course::whereIn('department_id', $allDeptIds)
                        ->where('level_id', $levelId)
                        ->where('semester_id', $semesterId)
                        ->get()
                        ->groupBy('department_id');
                ?>

                <?php $__currentLoopData = $allDeptIds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deptId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $dept = \App\Models\Department::find($deptId);
                        $deptCourses = $courses->get($deptId, collect());
                    ?>

                    <?php if($dept): ?>
                        <div>
                            <h3 class="font-semibold text-center mb-1"><?php echo e($dept->name); ?> Courses</h3>
                            <table class="w-full border border-gray-300">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="border px-2 py-1 text-left">Code</th>
                                        <th class="border px-2 py-1 text-left">Title</th>
                                        <th class="border px-2 py-1 text-center">Credit</th>
                                        <th class="border px-2 py-1 text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $deptCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td class="border px-2 py-0.5 whitespace-nowrap"><?php echo e($course->code); ?></td>
                                            <td class="border px-2 py-0.5"><?php echo e($course->title); ?></td>
                                            <td class="border px-2 py-0.5 text-center"><?php echo e($course->credit); ?></td>
                                            <td class="border px-2 py-0.5 text-center"><?php echo e($course->course_status->getAlias()); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="4" class="border px-2 py-2 text-center">No courses</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

        </div>

        
          <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Head of Departments (H.O.Ds)</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-1 text-[10px]">
                 <?php $__currentLoopData = $allDeptIds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deptId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $dept = \App\Models\Department::find($deptId);
                        $headOfDepartment = $dept?->headOfDepartment;
                    ?>
                    <?php if($dept): ?>
                        <div class="border p-1">
                            <p><span class="font-semibold">Dept: </span><?php echo e($dept->name); ?></p>
                            <p>
                                <span class="font-semibold">Name: </span>
                                    <?php if($headOfDepartment): ?>
                                        <?php echo e($headOfDepartment->title?->getLabel()); ?>

                                        <?php echo e($headOfDepartment->name); ?>

                                        <?php if($headOfDepartment->qualification): ?>
                                            (<?php echo e(collect($headOfDepartment->qualification)
                                                ->map(fn($q) => \App\Enums\Qualification::from($q)->getLabel())
                                                ->join(', ')); ?>)
                                        <?php endif; ?>
                                    <?php else: ?>
                                        ____________________________________
                                    <?php endif; ?>
                            </p>
                            <p><span class="font-semibold">Sign & Date: </span> _____________________________</p>
                        </div>
                    <?php endif; ?>
                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>           
            </div>
        </div>
        

        
        <div class="fixed bottom-4 right-4 print:hidden">
            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']); ?>
                Print registration
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
        </div>

        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/documents/registration.blade.php ENDPATH**/ ?>