<?php

namespace App\Filament\Student\Clusters\Cpay\Resources;

use App\Enums\Role;
use App\Enums\FeeType;
use App\Models\Invoice;
use Filament\Tables\Table;
use App\Enums\InvoiceStatus;
use App\Models\Registration;
use App\Enums\AdmissionStatus;
use App\Settings\PortalSettings;
use Filament\Resources\Resource;
use App\Services\PaystackService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;
use App\Filament\Student\Clusters\Cpay;
use App\Services\CodeGenerationService;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Pages\SubNavigationPosition;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Student\Clusters\Cpay\Resources\PortalResource\Pages;
use App\Filament\Student\Clusters\Cpay\Resources\PortalResource\RelationManagers;

class PortalResource extends Resource
{
    protected static ?string $model = Registration::class;
    protected static ?string $modelLabel = 'portal';
    protected static ?string $pluralModelLabel = 'portal';
    protected static ?string $slug = 'portal';
    protected static ?int $navigationSort = 1;
    protected static ?string $cluster = Cpay::class;
    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', Auth::id())
            ->with(['portalInvoice' => fn($q) => $q->where('user_id', Auth::id())]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function table(Table $table): Table
    {
        $student = Auth::user();
        $portalFee = app(PortalSettings::class)->portal_fee;
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            // ->recordAction(null)
            ->defaultSort('registrations.created_at', 'desc')
            ->emptyStateHeading('No Portal Fees Yet')
            ->emptyStateDescription('Once portal fees are paid, it will appear here.')
            ->description('Check portal fee status per semester for result access.')
            ->filtersLayout(FiltersLayout::AboveContent)

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('schoolSession.name')->label('Session'),
                TextColumn::make('level.name')->label('Level'),
                TextColumn::make('semester.name')->label('Semester'),
                TextColumn::make('portalInvoice.total_amount')
                    ->prefix('₦')
                    ->formatStateUsing(fn($state) => number_format($state))
                    ->label('Amount')
                    ->placeholder('₦' . number_format($portalFee)),
                IconColumn::make('portalInvoice.invoice_status')
                    ->getStateUsing(fn($record) => $record->portalInvoice?->invoice_status ?? false)
                    ->label('Access')
                    ->boolean()
                    ->trueIcon('heroicon-m-check-circle')
                    ->falseIcon('heroicon-m-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

            ])
            ->actions([
                Action::make('pay')
                    ->visible(fn($record) => ! $record->portalInvoice?->invoice_status)
                    ->label('PAY')
                    ->icon('heroicon-o-credit-card')
                    ->button()
                    ->tooltip('Pay portal fee')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm Payment?')
                    ->modalDescription(fn() => new HtmlString('You are paying a total of <b>₦' . number_format($portalFee) . '</b> for <b>Portal Fee</b>. Would you like to proceed?'))
                    ->modalSubmitActionLabel('Proceed')
                    ->action(function ($livewire, $record) use ($portalFee, $student) {
                        try {
                            DB::transaction(function () use ($portalFee, $student, $livewire, $record) {
                                $item = [
                                    'item' => "Portal access",
                                    'amount' => $portalFee,
                                ];

                                $invoice = Invoice::create([
                                    'user_id' => $student->id,
                                    'payable_id' => $record->id,
                                    'payable_type' => Registration::class,
                                    'number' => app(CodeGenerationService::class)->generateInvoiceNumber(),
                                    'reference' => app(CodeGenerationService::class)->generateReference(),
                                    'description' => [$item],
                                    'total_amount' => $portalFee,
                                    'invoice_status' => InvoiceStatus::INITIATED,
                                    'fee_type' => FeeType::PORTAL,
                                ]);

                                $response = (app(PaystackService::class))->initializePayment($student->email, $portalFee, $invoice->reference);
                                $responseData = $response->json();

                                if ($response->successful() && $responseData && $responseData['status']) {
                                    $livewire->dispatch('paystack-popup', accessCode: $responseData['data']['access_code'], invoiceId: $invoice->id);
                                } else {
                                    $errorMessage = $responseData['message'] ?? 'Payment initialization failed with Paystack.';
                                    throw new \Exception($errorMessage);
                                }
                            });
                        } catch (\Exception $e) {
                            Notification::make()
                                ->danger()
                                ->title('Payment Initiation Failed')
                                ->body('Unable to process payment. Please try again. If the issue persists, contact the school admin.')
                                ->persistent()
                                ->send();

                            Log::error('Paystack Payment Initiation Failed', [
                                'error' => $e->getMessage(),
                                'record' => $record
                            ]);
                        }
                    }),
                Action::make('pending')
                    ->visible(fn($record) => $record->portalInvoice?->invoice_status === InvoiceStatus::INITIATED)
                    ->label('PENDING')
                    ->disabled()
                    ->icon('heroicon-o-check-circle')
                    ->color('gray')
                    ->button()
                    ->modalSubmitAction(false),
                ActionGroup::make([
                    Action::make('print')
                        ->icon('heroicon-m-printer')
                        ->action(function ($record, $livewire) {

                            // Generate signed URL
                            $url = URL::signedRoute('invoice.print', ['invoice' => $record->portalInvoice->id]);

                            $livewire->js("(function() {
                                        const newWindow = window.open(
                                            '$url',
                                            'Invoice',
                                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                        );
    
                                        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                            alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                        } else {
                                            newWindow.focus();
                                        }
                                    })();");
                        }),
                    Action::make('download')
                        ->icon('heroicon-m-document-arrow-down')
                        ->action(function ($record, $livewire) {

                            $url = URL::signedRoute('invoice.download', ['invoice' => $record->portalInvoice->id]);

                            $livewire->js("window.location.href = '$url';");
                        }),
                ])
                    ->visible(fn($record) => $record->portalInvoice?->invoice_status === InvoiceStatus::PAID)
                    ->label('PAID')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->button()
                    ->tooltip('Fee paid, export receipt.'),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePortal::route('/'),
        ];
    }
}
