{"__meta": {"id": "01K6HVWRNS173ZJJQ9M0D1T70V", "datetime": "2025-10-02 08:06:27", "utime": **********.385462, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759388786.555393, "end": **********.385521, "duration": 0.8301279544830322, "duration_str": "830ms", "measures": [{"label": "Booting", "start": 1759388786.555393, "relative_start": 0, "end": **********.160509, "relative_end": **********.160509, "duration": 0.****************, "duration_str": "605ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.160537, "relative_start": 0.****************, "end": **********.385528, "relative_end": 7.152557373046875e-06, "duration": 0.****************, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.177787, "relative_start": 0.****************, "end": **********.178862, "relative_end": **********.178862, "duration": 0.0010750293731689453, "duration_str": "1.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.384096, "relative_start": 0.****************, "end": **********.384526, "relative_end": **********.384526, "duration": 0.00043010711669921875, "duration_str": "430μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7102824, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 22, "nb_statements": 21, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03205, "accumulated_duration_str": "32.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.188921, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kk0GIlRlv9zr9vZuu7tNYy1nEYnQWyJKdyu4oKwp' limit 1", "type": "query", "params": [], "bindings": ["kk0GIlRlv9zr9vZuu7tNYy1nEYnQWyJKdyu4oKwp"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.19024, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 9.953}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.215542, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 9.953, "width_percent": 5.335}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.2422872, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 15.289, "width_percent": 2.527}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.2494152, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 17.816, "width_percent": 3.027}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_start` asc", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 26}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.259, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:26", "source": {"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=26", "ajax": false, "filename": "AcademicCalendarService.php", "line": "26"}, "connection": "racoed", "explain": null, "start_percent": 20.842, "width_percent": 4.867}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.266035, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 25.71, "width_percent": 14.665}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-02' and date(`semester_end`) >= '2025-10-02' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02", "2025-10-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.276595, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 40.374, "width_percent": 2.715}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.282924, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 43.089, "width_percent": 5.585}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-02' and date(`semester_end`) >= '2025-10-02' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02", "2025-10-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.291343, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 48.674, "width_percent": 2.808}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.295469, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 51.482, "width_percent": 2.527}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.303816, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 54.009, "width_percent": 7.083}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and `semester_start` > '2025-10-02 08:06:27' order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02 08:06:27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 78}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.309848, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:78", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=78", "ajax": false, "filename": "AcademicCalendarService.php", "line": "78"}, "connection": "racoed", "explain": null, "start_percent": 61.092, "width_percent": 3.307}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, {"index": 17, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 84}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}], "start": **********.3147252, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:206", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=206", "ajax": false, "filename": "AcademicCalendarService.php", "line": "206"}, "connection": "racoed", "explain": null, "start_percent": 64.399, "width_percent": 2.621}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 89}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.325952, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:89", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=89", "ajax": false, "filename": "AcademicCalendarService.php", "line": "89"}, "connection": "racoed", "explain": null, "start_percent": 67.02, "width_percent": 4.93}, {"sql": "select * from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.332724, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:95", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=95", "ajax": false, "filename": "AcademicCalendarService.php", "line": "95"}, "connection": "racoed", "explain": null, "start_percent": 71.95, "width_percent": 6.521}, {"sql": "select * from `semesters` where `semesters`.`id` in (1) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 25, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.342544, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:95", "source": {"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=95", "ajax": false, "filename": "AcademicCalendarService.php", "line": "95"}, "connection": "racoed", "explain": null, "start_percent": 78.471, "width_percent": 3.588}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.348257, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 82.059, "width_percent": 2.683}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 and `semester_start` > '2025-10-02 08:06:27' order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-02 08:06:27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 113}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.35658, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:113", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=113", "ajax": false, "filename": "AcademicCalendarService.php", "line": "113"}, "connection": "racoed", "explain": null, "start_percent": 84.743, "width_percent": 2.871}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, {"index": 17, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 119}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}], "start": **********.3608959, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:206", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=206", "ajax": false, "filename": "AcademicCalendarService.php", "line": "206"}, "connection": "racoed", "explain": null, "start_percent": 87.613, "width_percent": 2.371}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.365454, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:124", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=124", "ajax": false, "filename": "AcademicCalendarService.php", "line": "124"}, "connection": "racoed", "explain": null, "start_percent": 89.984, "width_percent": 7.644}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 129}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.372928, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:129", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=129", "ajax": false, "filename": "AcademicCalendarService.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 97.629, "width_percent": 2.371}]}, "models": {"data": {"App\\Models\\SchoolSession": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Semester": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.staff.widgets.academic-calendar #Hp7Pcb3T2lLa27zgQhUE": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.staff.widgets.academic-calendar\"\n  \"component\" => \"App\\Filament\\Staff\\Widgets\\AcademicCalendar\"\n  \"id\" => \"Hp7Pcb3T2lLa27zgQhUE\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "832ms", "peak_memory": "10MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1182160047 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1182160047\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1969956164 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3YfYKYZjPZvBUamiOdAn79jGfpbW39rLWDEbzZVc</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"325 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;Hp7Pcb3T2lLa27zgQhUE&quot;,&quot;name&quot;:&quot;app.filament.staff.widgets.academic-calendar&quot;,&quot;path&quot;:&quot;staff\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;90cbbacd6bcf0560094e3fa8c33104df4fd96b3e3506a7162eb01008d33b4fae&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6ImhnTUNSaUl2Sm9tbU5jM0YzMVpFIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiIxNzlmOTNhYjZmOGY1YzI1Y2U1MzA3ZmFhMjVjNjA0NzJlMWFkOWNiYTkwZjM0OWRiZWRjMDdiOTdlNDlhYTkzIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969956164\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-261362935 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IkRpYnNIRURwSzBORzZybDVveEdaSVE9PSIsInZhbHVlIjoiWXI4V09YZ0pxVTA5STNtMCt3TjRnUDVoSDJKMDhPZzlGd1BHMnZUS3hPSVg0Vk9hcEY3UXdWcG1kbzloc0ptT3FFMVFGcVNJeE5vcGNCZFRxd3gvSW9yVUZVUC9RTDViYkIrZTE2cUovbWg5QTRFbzdyZ2R1WSsxamR0MnBNelgiLCJtYWMiOiI1NDQ2ODU4MTRlMjY5Yjc1M2RiOTI0YTI3MTZlN2U2NWU2ZjUwMjVlNDhhYzcyZDJmZTYzZTNjM2NlYmZkNjYxIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6InAvRjJhYnk0SXNjUmoxRFVsZllZS2c9PSIsInZhbHVlIjoiaFN1Uk05dVlwZ1liQmFvT3dnQisrOS90L2hoM3gzUHlkNjJJa05KL2xKall2T2xkVllVK1UwTGM5V1NtcDlxTDRyMWptNitpWHdLY2J6Z2pDUUZvYXk0Y0FmUXgxbHpYYS81ZFg5L0JwNjAvd0UzMU1HOE0wbEd0RUorNXRVeWQiLCJtYWMiOiIwYTk3NTQ5NjMxYTUzZDk4YzY1YzYwOGM4YTI4ODhjZTY1ODI5MGMzNzQ1MTM0ZjA1NjFhNzZiYzA4ODhiZDM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/staff/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">769</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261362935\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1532245688 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3YfYKYZjPZvBUamiOdAn79jGfpbW39rLWDEbzZVc</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kk0GIlRlv9zr9vZuu7tNYy1nEYnQWyJKdyu4oKwp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532245688\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1333022759 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 02 Oct 2025 07:06:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333022759\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-165231233 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3YfYKYZjPZvBUamiOdAn79jGfpbW39rLWDEbzZVc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">https://portal.racoed.test/student</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/staff/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K6HVW8YF4F1PGBJ9XFC50TMZ</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165231233\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}