{"__meta": {"id": "01K66JRZGQWQYE6WKBZSH6G447", "datetime": "2025-09-27 22:55:27", "utime": **********.383968, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759010125.504471, "end": **********.383991, "duration": 1.8795199394226074, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1759010125.504471, "relative_start": 0, "end": **********.197102, "relative_end": **********.197102, "duration": 0.****************, "duration_str": "693ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.197135, "relative_start": 0.****************, "end": **********.383994, "relative_end": 3.0994415283203125e-06, "duration": 1.***************, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.221146, "relative_start": 0.****************, "end": **********.224032, "relative_end": **********.224032, "duration": 0.0028858184814453125, "duration_str": "2.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.044304, "relative_start": 1.****************, "end": **********.044304, "relative_end": **********.044304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.060879, "relative_start": 1.****************, "end": **********.060879, "relative_end": **********.060879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.073979, "relative_start": 1.5695078372955322, "end": **********.073979, "relative_end": **********.073979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.092713, "relative_start": 1.5882420539855957, "end": **********.092713, "relative_end": **********.092713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.100821, "relative_start": 1.5963499546051025, "end": **********.100821, "relative_end": **********.100821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.tables.result", "start": **********.112869, "relative_start": 1.6083979606628418, "end": **********.112869, "relative_end": **********.112869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.310335, "relative_start": 1.805863857269287, "end": **********.310335, "relative_end": **********.310335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.316527, "relative_start": 1.8120558261871338, "end": **********.316527, "relative_end": **********.316527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.324739, "relative_start": 1.820267915725708, "end": **********.324739, "relative_end": **********.324739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.348151, "relative_start": 1.8436799049377441, "end": **********.348151, "relative_end": **********.348151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.369686, "relative_start": 1.8652148246765137, "end": **********.369686, "relative_end": **********.369686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.38028, "relative_start": 1.8758089542388916, "end": **********.382063, "relative_end": **********.382063, "duration": 0.0017828941345214844, "duration_str": "1.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8030336, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.044195, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.060789, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.073904, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.092588, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.100715, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.tables.result", "param_count": null, "params": [], "start": **********.112751, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.phpfilament.tables.result", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fresult.blade.php&line=1", "ajax": false, "filename": "result.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.31023, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.31645, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.32463, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.348039, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.369539, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}]}, "queries": {"count": 223, "nb_statements": 220, "nb_visible_statements": 223, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.31229999999999986, "accumulated_duration_str": "312ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 120 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.234869, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nsyhbB2bXjyBRJypBaDi2ILocmhzpijgwmGjiPkv' limit 1", "type": "query", "params": [], "bindings": ["nsyhbB2bXjyBRJypBaDi2ILocmhzpijgwmGjiPkv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.237477, "duration": 0.02452, "duration_str": "24.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 7.851}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.2891438, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 7.851, "width_percent": 0.644}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.3242528, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 8.495, "width_percent": 0.919}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` in (2, 3) and `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.331448, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 9.414, "width_percent": 0.704}, {"sql": "select * from `levels` where `levels`.`id` in (1, 2) and `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.339077, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 10.118, "width_percent": 0.666}, {"sql": "select `school_sessions`.*, `registrations`.`user_id` as `laravel_through_key` from `school_sessions` inner join `registrations` on `registrations`.`school_session_id` = `school_sessions`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 91}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.3668592, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:91", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=91", "ajax": false, "filename": "ResultResource.php", "line": "91"}, "connection": "racoed", "explain": null, "start_percent": 10.785, "width_percent": 0.423}, {"sql": "select `levels`.*, `registrations`.`user_id` as `laravel_through_key` from `levels` inner join `registrations` on `registrations`.`level_id` = `levels`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.3732939, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:110", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=110", "ajax": false, "filename": "ResultResource.php", "line": "110"}, "connection": "racoed", "explain": null, "start_percent": 11.207, "width_percent": 0.416}, {"sql": "select `semesters`.*, `registrations`.`user_id` as `laravel_through_key` from `semesters` inner join `registrations` on `registrations`.`semester_id` = `semesters`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 117}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.378696, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:117", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=117", "ajax": false, "filename": "ResultResource.php", "line": "117"}, "connection": "racoed", "explain": null, "start_percent": 11.623, "width_percent": 0.503}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.3902938, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 12.126, "width_percent": 0.64}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.396785, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 12.767, "width_percent": 0.275}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = '1' and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", "1", "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.4058661, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 13.042, "width_percent": 0.829}, {"sql": "select * from `applications` where `applications`.`user_id` = 22 and `applications`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 337}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 452}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4135072, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "User.php:337", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=337", "ajax": false, "filename": "User.php", "line": "337"}, "connection": "racoed", "explain": null, "start_percent": 13.871, "width_percent": 0.592}, {"sql": "select * from `guardians` where `guardians`.`user_id` = 22 and `guardians`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 452}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.420239, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "User.php:344", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=344", "ajax": false, "filename": "User.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 14.464, "width_percent": 0.797}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = '1' and `level_id` = '1')) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [22, 1, "2", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 121}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.428916, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ManageResults.php:126", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=126", "ajax": false, "filename": "ManageResults.php", "line": "126"}, "connection": "racoed", "explain": null, "start_percent": 15.261, "width_percent": 0.429}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.440284, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 15.69, "width_percent": 0.288}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.4448931, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 15.978, "width_percent": 0.263}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = '1' and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", "1", "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.450044, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 16.241, "width_percent": 0.474}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = '1' and `level_id` = '1')) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [22, 1, "2", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 121}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.4586642, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "ManageResults.php:126", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=126", "ajax": false, "filename": "ManageResults.php", "line": "126"}, "connection": "racoed", "explain": null, "start_percent": 16.715, "width_percent": 0.48}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 82}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.467959, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:30", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=30", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "30"}, "connection": "racoed", "explain": null, "start_percent": 17.195, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.4700441, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 17.195, "width_percent": 0.387}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.477027, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 17.582, "width_percent": 0.282}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = '1' and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", "1", "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.4825351, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 17.864, "width_percent": 0.426}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = '1' and `level_id` = '1')) and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1, "2", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 70}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}], "start": **********.49134, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ManageResults.php:70", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=70", "ajax": false, "filename": "ManageResults.php", "line": "70"}, "connection": "racoed", "explain": null, "start_percent": 18.29, "width_percent": 0.461}, {"sql": "select `max_score`, `name` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 72}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}], "start": **********.496684, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "ManageResults.php:72", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=72", "ajax": false, "filename": "ManageResults.php", "line": "72"}, "connection": "racoed", "explain": null, "start_percent": 18.751, "width_percent": 0.608}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 192}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.502425, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 19.36, "width_percent": 0.391}, {"sql": "select * from `registrations` where `user_id` = 22 and `school_session_id` = '2' and `semester_id` = '1' and `level_id` = '1' limit 1", "type": "query", "params": [], "bindings": [22, "2", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 407}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 196}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.508752, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:407", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=407", "ajax": false, "filename": "ResultResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 19.75, "width_percent": 0.32}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `semester_id` = '1' and `level_id` = '1' and `department_id` = '16' order by `code` asc", "type": "query", "params": [], "bindings": ["1", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.513676, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:207", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=207", "ajax": false, "filename": "ResultResource.php", "line": "207"}, "connection": "racoed", "explain": null, "start_percent": 20.07, "width_percent": 0.925}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 209}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.5219429, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:209", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=209", "ajax": false, "filename": "ResultResource.php", "line": "209"}, "connection": "racoed", "explain": null, "start_percent": 20.996, "width_percent": 0.583}, {"sql": "select * from `scores` where `course_id` = 326 and `registration_id` = 35", "type": "query", "params": [], "bindings": [326, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.5283198, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 21.579, "width_percent": 0.631}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.533931, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 22.209, "width_percent": 0.263}, {"sql": "select `total` from `total_scores` where `course_id` = 326 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [326, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.5406609, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 22.472, "width_percent": 0.519}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 368}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}], "start": **********.546667, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:368", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 368}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=368", "ajax": false, "filename": "ResultResource.php", "line": "368"}, "connection": "racoed", "explain": null, "start_percent": 22.991, "width_percent": 0.256}, {"sql": "select * from `scores` where `course_id` = 327 and `registration_id` = 35", "type": "query", "params": [], "bindings": [327, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.552234, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 23.247, "width_percent": 0.496}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.5584772, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 23.743, "width_percent": 0.269}, {"sql": "select `total` from `total_scores` where `course_id` = 327 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [327, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.562357, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 24.012, "width_percent": 0.298}, {"sql": "select * from `scores` where `course_id` = 328 and `registration_id` = 35", "type": "query", "params": [], "bindings": [328, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.566828, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 24.31, "width_percent": 0.384}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.573411, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 24.694, "width_percent": 0.259}, {"sql": "select `total` from `total_scores` where `course_id` = 328 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [328, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.57815, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 24.954, "width_percent": 0.32}, {"sql": "select * from `scores` where `course_id` = 329 and `registration_id` = 35", "type": "query", "params": [], "bindings": [329, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.582524, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 25.274, "width_percent": 0.314}, {"sql": "select * from `assessments` where `assessments`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.588031, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 25.588, "width_percent": 0.323}, {"sql": "select `total` from `total_scores` where `course_id` = 329 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [329, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.592725, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 25.911, "width_percent": 0.327}, {"sql": "select * from `scores` where `course_id` = 330 and `registration_id` = 35", "type": "query", "params": [], "bindings": [330, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.597125, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 26.238, "width_percent": 0.391}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6022148, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 26.628, "width_percent": 0.243}, {"sql": "select `total` from `total_scores` where `course_id` = 330 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [330, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.607637, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 26.872, "width_percent": 0.327}, {"sql": "select * from `scores` where `course_id` = 331 and `registration_id` = 35", "type": "query", "params": [], "bindings": [331, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6120698, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 27.198, "width_percent": 0.403}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6167312, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 27.602, "width_percent": 0.269}, {"sql": "select `total` from `total_scores` where `course_id` = 331 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [331, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.621514, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 27.871, "width_percent": 0.327}, {"sql": "select * from `scores` where `course_id` = 332 and `registration_id` = 35", "type": "query", "params": [], "bindings": [332, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.626921, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 28.197, "width_percent": 0.634}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.633492, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 28.831, "width_percent": 0.256}, {"sql": "select `total` from `total_scores` where `course_id` = 332 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [332, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.638651, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 29.087, "width_percent": 0.301}, {"sql": "select * from `scores` where `course_id` = 333 and `registration_id` = 35", "type": "query", "params": [], "bindings": [333, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.643028, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 29.388, "width_percent": 0.375}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.648821, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 29.763, "width_percent": 0.263}, {"sql": "select `total` from `total_scores` where `course_id` = 333 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [333, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.65406, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 30.026, "width_percent": 0.394}, {"sql": "select * from `scores` where `course_id` = 334 and `registration_id` = 35", "type": "query", "params": [], "bindings": [334, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.659232, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 30.419, "width_percent": 0.413}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 27, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.66527, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 30.833, "width_percent": 0.336}, {"sql": "select `total` from `total_scores` where `course_id` = 334 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [334, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 73}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6698332, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 31.169, "width_percent": 0.375}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 81}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.6756792, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:381", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=381", "ajax": false, "filename": "ResultResource.php", "line": "381"}, "connection": "racoed", "explain": null, "start_percent": 31.543, "width_percent": 0.259}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 391}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 270}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 270}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 82}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}], "start": **********.680437, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:391", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=391", "ajax": false, "filename": "ResultResource.php", "line": "391"}, "connection": "racoed", "explain": null, "start_percent": 31.803, "width_percent": 0.275}, {"sql": "select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 306}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.685147, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:306", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 306}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=306", "ajax": false, "filename": "ResultResource.php", "line": "306"}, "connection": "racoed", "explain": null, "start_percent": 32.078, "width_percent": 0.439}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6921241, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 32.517, "width_percent": 0.317}, {"sql": "select `credit` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.696779, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:315", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=315", "ajax": false, "filename": "ResultResource.php", "line": "315"}, "connection": "racoed", "explain": null, "start_percent": 32.834, "width_percent": 0.608}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.703776, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 33.442, "width_percent": 0.41}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.7096388, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 33.852, "width_percent": 0.298}, {"sql": "select `credit` from `courses` where `level_id` = 2 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [2, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.7160048, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:315", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=315", "ajax": false, "filename": "ResultResource.php", "line": "315"}, "connection": "racoed", "explain": null, "start_percent": 34.15, "width_percent": 0.628}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 85}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.724026, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 34.777, "width_percent": 0.323}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 277}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.7286658, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:277", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=277", "ajax": false, "filename": "ResultResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 35.101, "width_percent": 0.253}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.733263, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 35.354, "width_percent": 0.285}, {"sql": "select `id`, `credit` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.740189, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:288", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=288", "ajax": false, "filename": "ResultResource.php", "line": "288"}, "connection": "racoed", "explain": null, "start_percent": 35.639, "width_percent": 0.551}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 326 limit 1", "type": "query", "params": [], "bindings": [35, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.7471511, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 36.19, "width_percent": 0.327}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 327 limit 1", "type": "query", "params": [], "bindings": [35, 327], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.7531872, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 36.516, "width_percent": 0.423}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 328 limit 1", "type": "query", "params": [], "bindings": [35, 328], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.759691, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 36.939, "width_percent": 0.323}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 329 limit 1", "type": "query", "params": [], "bindings": [35, 329], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.765613, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 37.262, "width_percent": 0.317}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 330 limit 1", "type": "query", "params": [], "bindings": [35, 330], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.774689, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 37.579, "width_percent": 0.362}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 331 limit 1", "type": "query", "params": [], "bindings": [35, 331], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.78091, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 37.941, "width_percent": 0.349}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 332 limit 1", "type": "query", "params": [], "bindings": [35, 332], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.788554, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 38.29, "width_percent": 0.365}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 333 limit 1", "type": "query", "params": [], "bindings": [35, 333], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.7951431, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 38.655, "width_percent": 0.333}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 334 limit 1", "type": "query", "params": [], "bindings": [35, 334], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8012412, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 38.988, "width_percent": 0.32}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.80771, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 39.308, "width_percent": 0.282}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8141031, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 39.59, "width_percent": 0.311}, {"sql": "select `id`, `credit` from `courses` where `level_id` = 2 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [2, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.8204, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:288", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=288", "ajax": false, "filename": "ResultResource.php", "line": "288"}, "connection": "racoed", "explain": null, "start_percent": 39.901, "width_percent": 0.519}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 343 limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8263788, "duration": 0.03484, "duration_str": "34.84ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 40.419, "width_percent": 11.156}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 344 limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8646019, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 51.575, "width_percent": 0.24}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 345 limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.868829, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 51.816, "width_percent": 0.269}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 346 limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.872943, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 52.085, "width_percent": 0.224}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 347 limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.876939, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 52.309, "width_percent": 0.227}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 348 limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.880753, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 52.536, "width_percent": 0.221}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 349 limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8845642, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 52.757, "width_percent": 0.208}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 86}, {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.888278, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 52.965, "width_percent": 0.208}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 1 and `school_session_id` = 2 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [1, 2, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.892413, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 53.173, "width_percent": 0.224}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 2 and `school_session_id` = 2 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [2, 2, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.896127, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 53.397, "width_percent": 0.205}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 1 and `school_session_id` = 3 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [1, 3, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.899986, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 53.602, "width_percent": 0.221}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 2 and `school_session_id` = 3 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [2, 3, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.903968, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 53.823, "width_percent": 0.211}, {"sql": "select `id`, `code` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 344}, {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.907601, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:344", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=344", "ajax": false, "filename": "ResultResource.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 54.035, "width_percent": 0.387}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 326) limit 1", "type": "query", "params": [], "bindings": [35, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.9119651, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 54.422, "width_percent": 0.215}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 327) limit 1", "type": "query", "params": [], "bindings": [35, 327], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.915787, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 54.637, "width_percent": 0.218}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 328) limit 1", "type": "query", "params": [], "bindings": [35, 328], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.919574, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 54.854, "width_percent": 0.211}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 329) limit 1", "type": "query", "params": [], "bindings": [35, 329], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.922934, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 55.066, "width_percent": 0.199}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 330) limit 1", "type": "query", "params": [], "bindings": [35, 330], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.926764, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 55.264, "width_percent": 0.269}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 331) limit 1", "type": "query", "params": [], "bindings": [35, 331], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.930646, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 55.533, "width_percent": 0.215}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 332) limit 1", "type": "query", "params": [], "bindings": [35, 332], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.934099, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 55.748, "width_percent": 0.221}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 333) limit 1", "type": "query", "params": [], "bindings": [35, 333], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 91}, {"index": 19, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 26}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.938214, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 55.969, "width_percent": 0.237}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942237, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.206, "width_percent": 0.224}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943851, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.43, "width_percent": 0.596}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947154, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.025, "width_percent": 0.387}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9495609, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.413, "width_percent": 0.288}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951952, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.701, "width_percent": 0.266}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954199, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.967, "width_percent": 0.378}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.95638, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.345, "width_percent": 0.301}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958431, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.646, "width_percent": 0.282}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960446, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.927, "width_percent": 0.298}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (?, ?, ?) on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.968734, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.225, "width_percent": 1.611}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 106}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.984064, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:39", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=39", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "39"}, "connection": "racoed", "explain": null, "start_percent": 60.836, "width_percent": 0}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = ? and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99374, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.836, "width_percent": 0.218}, {"sql": "select * from `semesters` where `semesters`.`id` = ? and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.996287, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.053, "width_percent": 0.32}, {"sql": "select * from `levels` where `levels`.`id` = ? and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9986, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.374, "width_percent": 0.394}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.001106, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.768, "width_percent": 0.765}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = ? and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.004862, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.533, "width_percent": 0.208}, {"sql": "select * from `semesters` where `semesters`.`id` = ? and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0065951, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.741, "width_percent": 0.208}, {"sql": "select * from `levels` where `levels`.`id` = ? and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008243, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.949, "width_percent": 0.215}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.009824, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.164, "width_percent": 0.205}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.015824, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.369, "width_percent": 0.227}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.017178, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.596, "width_percent": 0.211}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `school_session_id` = ? and `semester_id` = ? and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0190742, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.807, "width_percent": 0.711}, {"sql": "select * from `users` where `id` = ? and `role` = ? and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?)) and `users`.`deleted_at` is null order by `users`.`id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0232081, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.518, "width_percent": 0.387}, {"sql": "select `semesters`.`name`, `semesters`.`id` from `semesters` inner join `registrations` on `registrations`.`semester_id` = `semesters`.`id` where `semesters`.`deleted_at` is null order by `semesters`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0688822, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.906, "width_percent": 0.48}, {"sql": "select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `is_active` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.079959, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.386, "width_percent": 0.275}, {"sql": "select * from `programmes` where `programmes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0820389, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.661, "width_percent": 0.451}, {"sql": "select * from `departments` where `id` in (?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.084348, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.113, "width_percent": 0.279}, {"sql": "select `name`, `id` from `departments` where `id` in (?) or `is_edu` = ? or `is_gse` = ? order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.086315, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.391, "width_percent": 0.56}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1142771, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.952, "width_percent": 0.493}, {"sql": "select `max_score`, `name` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1163568, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.445, "width_percent": 0.186}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1175811, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.63, "width_percent": 0.221}, {"sql": "select * from `registrations` where `user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1193168, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.851, "width_percent": 0.525}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `semester_id` = ? and `level_id` = ? and `department_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.122259, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.377, "width_percent": 0.57}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.124803, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.947, "width_percent": 0.221}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1263068, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.167, "width_percent": 0.365}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.12829, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.533, "width_percent": 0.237}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1298862, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.769, "width_percent": 0.266}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1320379, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.035, "width_percent": 0.343}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.134365, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.378, "width_percent": 0.221}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.136916, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.599, "width_percent": 0.688}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.140772, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.287, "width_percent": 0.451}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1432471, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.739, "width_percent": 0.256}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.145396, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.995, "width_percent": 0.336}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.147781, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.331, "width_percent": 0.301}, {"sql": "select * from `assessments` where `assessments`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.149769, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.632, "width_percent": 0.231}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.151727, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.863, "width_percent": 0.263}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1544828, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.125, "width_percent": 0.752}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.157901, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.878, "width_percent": 0.24}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.159977, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.118, "width_percent": 0.247}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.162261, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.364, "width_percent": 0.327}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.164535, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.691, "width_percent": 0.221}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.166414, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.912, "width_percent": 0.298}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.169024, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.21, "width_percent": 0.506}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.171912, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.716, "width_percent": 0.301}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.174016, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.017, "width_percent": 0.314}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.176012, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.33, "width_percent": 0.381}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.178029, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.711, "width_percent": 0.227}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.179588, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.939, "width_percent": 0.272}, {"sql": "select * from `scores` where `course_id` = ? and `registration_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1816428, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.211, "width_percent": 0.365}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.184035, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.576, "width_percent": 0.211}, {"sql": "select `total` from `total_scores` where `course_id` = ? and `registration_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.186265, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.787, "width_percent": 0.583}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.189645, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.37, "width_percent": 0.32}, {"sql": "select `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.191304, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.69, "width_percent": 0.586}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194147, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.276, "width_percent": 0.259}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.195529, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.536, "width_percent": 0.253}, {"sql": "select `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.196928, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.789, "width_percent": 0.66}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.199949, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.448, "width_percent": 0.269}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2012799, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.717, "width_percent": 0.186}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.203095, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.903, "width_percent": 0.615}, {"sql": "select `id`, `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.205963, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.518, "width_percent": 0.634}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.209348, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.152, "width_percent": 0.253}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.211413, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.405, "width_percent": 0.224}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.213294, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.629, "width_percent": 0.221}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215058, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.85, "width_percent": 0.279}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2167602, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.128, "width_percent": 0.259}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.218805, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.388, "width_percent": 0.263}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.221235, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.65, "width_percent": 0.375}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2235699, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.025, "width_percent": 0.301}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.225375, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.326, "width_percent": 0.282}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2271059, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.608, "width_percent": 0.474}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.229189, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.082, "width_percent": 0.247}, {"sql": "select `id`, `credit` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.230633, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.328, "width_percent": 0.576}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.233547, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.905, "width_percent": 0.33}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.236137, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.234, "width_percent": 0.583}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2393599, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.817, "width_percent": 0.288}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.241354, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.105, "width_percent": 0.221}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2431052, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.326, "width_percent": 0.211}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.244821, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.538, "width_percent": 0.211}, {"sql": "select `total` from `total_scores` where `registration_id` = ? and `course_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.246551, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.749, "width_percent": 0.205}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2481248, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.954, "width_percent": 0.253}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249991, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.207, "width_percent": 0.224}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2514338, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.431, "width_percent": 0.25}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.253473, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.681, "width_percent": 0.512}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = ? and `school_session_id` = ? and `department_id` = ? and `is_published` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.256107, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.193, "width_percent": 0.285}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.25796, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.478, "width_percent": 0.432}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.260714, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.91, "width_percent": 0.311}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.262869, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.221, "width_percent": 0.285}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2649229, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.506, "width_percent": 0.304}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.267009, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.81, "width_percent": 0.279}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2692761, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.089, "width_percent": 0.484}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.272215, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.572, "width_percent": 0.346}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.274504, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.918, "width_percent": 0.304}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.276626, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.222, "width_percent": 0.279}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.278701, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.501, "width_percent": 0.288}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.280828, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.789, "width_percent": 0.679}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.284264, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.468, "width_percent": 0.371}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287108, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.839, "width_percent": 0.442}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2897391, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.281, "width_percent": 0.333}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.291897, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.614, "width_percent": 0.506}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.294572, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.12, "width_percent": 0.279}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.296354, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.399, "width_percent": 0.269}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.298148, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.668, "width_percent": 0.253}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.312576, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.921, "width_percent": 0.496}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.334035, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.417, "width_percent": 0.291}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.335925, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.708, "width_percent": 0.884}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `school_session_id` = ? and `semester_id` = ? and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.340598, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.592, "width_percent": 0.544}, {"sql": "select * from `users` where `id` = ? and `role` = ? and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?)) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.344828, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.136, "width_percent": 0.455}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.359272, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.591, "width_percent": 0.301}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = ? and `semester_id` = ? and `department_id` = ? and `is_published` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3610458, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.892, "width_percent": 0.237}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and `school_session_id` = ? and `semester_id` = ? and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3632622, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.129, "width_percent": 0.426}, {"sql": "select * from `users` where `id` = ? and `role` = ? and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?)) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.367011, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.555, "width_percent": 0.445}]}, "models": {"data": {"App\\Models\\Course": {"value": 114, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 62, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\Grade": {"value": 37, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\Score": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FScore.php&line=1", "ajax": false, "filename": "Score.php", "line": "?"}}, "App\\Models\\Assessment": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FAssessment.php&line=1", "ajax": false, "filename": "Assessment.php", "line": "?"}}, "App\\Models\\Registration": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\User": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Department": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Level": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\Semester": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\Guardian": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGuardian.php&line=1", "ajax": false, "filename": "Guardian.php", "line": "?"}}, "App\\Models\\Programme": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=1", "ajax": false, "filename": "Programme.php", "line": "?"}}}, "count": 323, "is_counter": true}, "livewire": {"data": {"app.filament.student.resources.result-resource.pages.manage-results #cilngnuAxmKGs7xV0OBQ": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"result_filter\" => array:4 [\n        \"school_session_id\" => \"2\"\n        \"level_id\" => \"1\"\n        \"semester_id\" => \"1\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => []\n    \"defaultActionArguments\" => []\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => null\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"result_filter\" => array:4 [\n        \"school_session_id\" => \"2\"\n        \"level_id\" => \"1\"\n        \"semester_id\" => \"1\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.student.resources.result-resource.pages.manage-results\"\n  \"component\" => \"App\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults\"\n  \"id\" => \"cilngnuAxmKGs7xV0OBQ\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults@mountAction<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:159-212</a>", "middleware": "web", "duration": "1.93s", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1918139336 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1918139336\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-781551557 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gqfRTjsDmYfSsFz8g6QX4srASvIuTkWXP33yHQbl</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1879 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;result_filter&quot;:[{&quot;school_session_id&quot;:&quot;2&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:null,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;result_filter&quot;:[{&quot;school_session_id&quot;:&quot;2&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;cilngnuAxmKGs7xV0OBQ&quot;,&quot;name&quot;:&quot;app.filament.student.resources.result-resource.pages.manage-results&quot;,&quot;path&quot;:&quot;student\\/results&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;ebcd291fecbe8f66a46cabf6879b1dd528cb450d2fdc0c2ec552e6ea12a1e597&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">print</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781551557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-785059760 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6InFvWmNFRlFjR25QeURUaXdDWnd1RXc9PSIsInZhbHVlIjoiVU53ZWJWenM3dkRXdm4wVERET01WRHFDZkxRVEhpa1laaUxPQm9LNEM0OTJ6S3VoR3Q4MW91aFFBY29jYitzOVpKcTVmRlMxS21EeDdiQzM4bmQ3YlduT1pvZDNsUXNHeEQxdUJlQUU1UnZ0N2R2WmxFTmNYM1lHd0J3ZWRVNkkiLCJtYWMiOiJiNmI5YTY0ZDkxNDc4ZDRmOTJiNzk4ZTlmMGI1ZDdhOWFjOTJmZmNmMzg4MmQ2OGJhMDE1NDJjZGQ1YjA0N2E1IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6InZScVZWK1U3UWUxbTRYNFBGK1RIb2c9PSIsInZhbHVlIjoiM3JrUDRKdWtBU0RyUlNuZnRRYjQ0bE1BelpDTFVaeXpSMzdmRjdYTk1qRnhuQUlRcVZGV3JVZ0ZjY0NLM2Z4MCtodU5WbW9MK3BYVStxTEpZY1lXZi83L3NQRzJxR3R2QkV5ZVlCb3d1TEV3YzZBVEVkQWM3cGVBYkcrZS9JQlEiLCJtYWMiOiI2NmY4NWNkZTAzYzFiZmY3OWJjNDY1YzllZmUyOGE1ZjQxMGU4Y2FjMmRiYjExOWNlOGVjZDIxZWRlMGU2MjE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"220 characters\">https://portal.racoed.test/student/results?tableFilters[result_filter][school_session_id]=2&amp;tableFilters[result_filter][level_id]=1&amp;tableFilters[result_filter][semester_id]=1&amp;tableFilters[result_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2281</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785059760\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-984809943 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gqfRTjsDmYfSsFz8g6QX4srASvIuTkWXP33yHQbl</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nsyhbB2bXjyBRJypBaDi2ILocmhzpijgwmGjiPkv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984809943\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1304319871 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 27 Sep 2025 21:55:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304319871\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-909626712 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gqfRTjsDmYfSsFz8g6QX4srASvIuTkWXP33yHQbl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/student/results</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"252 characters\">https://portal.racoed.test/student/results?tableFilters%5Bresult_filter%5D%5Bschool_session_id%5D=2&amp;tableFilters%5Bresult_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageResults_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>result_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909626712\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}