{"__meta": {"id": "01K67F00CSPW06XFMVM3PQG30D", "datetime": "2025-09-28 07:08:37", "utime": **********.785577, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********16.51842, "end": **********.785596, "duration": 1.2671759128570557, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": ********16.51842, "relative_start": 0, "end": **********.031009, "relative_end": **********.031009, "duration": 0.****************, "duration_str": "513ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.031029, "relative_start": 0.****************, "end": **********.785599, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "755ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.045989, "relative_start": 0.***************, "end": **********.046843, "relative_end": **********.046843, "duration": 0.0008540153503417969, "duration_str": "854μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.322506, "relative_start": 0.****************, "end": **********.322506, "relative_end": **********.322506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.339091, "relative_start": 0.****************, "end": **********.339091, "relative_end": **********.339091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.384988, "relative_start": 0.8665680885314941, "end": **********.384988, "relative_end": **********.384988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.413232, "relative_start": 0.8948121070861816, "end": **********.413232, "relative_end": **********.413232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.418425, "relative_start": 0.9000051021575928, "end": **********.418425, "relative_end": **********.418425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.tables.result", "start": **********.432116, "relative_start": 0.9136960506439209, "end": **********.432116, "relative_end": **********.432116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.766025, "relative_start": 1.2476050853729248, "end": **********.766025, "relative_end": **********.766025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.770615, "relative_start": 1.252195119857788, "end": **********.770615, "relative_end": **********.770615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.778943, "relative_start": 1.2605230808258057, "end": **********.778943, "relative_end": **********.778943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.784563, "relative_start": 1.2661430835723877, "end": **********.78518, "relative_end": **********.78518, "duration": 0.0006170272827148438, "duration_str": "617μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7335880, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.322428, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.339026, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.384916, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.413156, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.418359, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.tables.result", "param_count": null, "params": [], "start": **********.432048, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.phpfilament.tables.result", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fresult.blade.php&line=1", "ajax": false, "filename": "result.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.765955, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.77055, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.778872, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 117, "nb_statements": 116, "nb_visible_statements": 117, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14521, "accumulated_duration_str": "145ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 16 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.053372, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = '8440Y34Bd5ZsZpI4FYdpE1CgYKG3KxlGlLXG6cgz' limit 1", "type": "query", "params": [], "bindings": ["8440Y34Bd5ZsZpI4FYdpE1CgYKG3KxlGlLXG6cgz"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.054272, "duration": 0.02761, "duration_str": "27.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 19.014}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.0894442, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 19.014, "width_percent": 1.522}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/InteractsWithHeaderActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\InteractsWithHeaderActions.php", "line": 27}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/InteractsWithHeaderActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\InteractsWithHeaderActions.php", "line": 25}], "start": **********.111385, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ManageResults.php:21", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=21", "ajax": false, "filename": "ManageResults.php", "line": "21"}, "connection": "racoed", "explain": null, "start_percent": 20.536, "width_percent": 0.585}, {"sql": "select * from `invoices` where `fee_type` = 1 and `invoices`.`payable_id` in (20, 35, 36, 185) and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 21}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/InteractsWithHeaderActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\InteractsWithHeaderActions.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}, {"index": 29, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/InteractsWithHeaderActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\InteractsWithHeaderActions.php", "line": 25}], "start": **********.1198719, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageResults.php:21", "source": {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource/Pages/ManageResults.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource%2FPages%2FManageResults.php&line=21", "ajax": false, "filename": "ManageResults.php", "line": "21"}, "connection": "racoed", "explain": null, "start_percent": 21.121, "width_percent": 0.744}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` in (2, 3) and `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.136849, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 21.865, "width_percent": 0.544}, {"sql": "select * from `levels` where `levels`.`id` in (1, 2) and `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.1456811, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:61", "source": {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=61", "ajax": false, "filename": "ResultResource.php", "line": "61"}, "connection": "racoed", "explain": null, "start_percent": 22.409, "width_percent": 0.551}, {"sql": "select `school_sessions`.*, `registrations`.`user_id` as `laravel_through_key` from `school_sessions` inner join `registrations` on `registrations`.`school_session_id` = `school_sessions`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 91}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.166286, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:91", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=91", "ajax": false, "filename": "ResultResource.php", "line": "91"}, "connection": "racoed", "explain": null, "start_percent": 22.96, "width_percent": 0.689}, {"sql": "select `levels`.*, `registrations`.`user_id` as `laravel_through_key` from `levels` inner join `registrations` on `registrations`.`level_id` = `levels`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.171026, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:110", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=110", "ajax": false, "filename": "ResultResource.php", "line": "110"}, "connection": "racoed", "explain": null, "start_percent": 23.649, "width_percent": 0.716}, {"sql": "select `semesters`.*, `registrations`.`user_id` as `laravel_through_key` from `semesters` inner join `registrations` on `registrations`.`semester_id` = `semesters`.`id` where `registrations`.`user_id` = 22 and `registrations`.`is_active` = 1 and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 117}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.180378, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:117", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=117", "ajax": false, "filename": "ResultResource.php", "line": "117"}, "connection": "racoed", "explain": null, "start_percent": 24.365, "width_percent": 0.613}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '2' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 152}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/BaseFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\BaseFilter.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilters.php", "line": 304}], "start": **********.197628, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:152", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=152", "ajax": false, "filename": "ResultResource.php", "line": "152"}, "connection": "racoed", "explain": null, "start_percent": 24.978, "width_percent": 0.379}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 159}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/BaseFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\BaseFilter.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilters.php", "line": 304}], "start": **********.2019942, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:159", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=159", "ajax": false, "filename": "ResultResource.php", "line": "159"}, "connection": "racoed", "explain": null, "start_percent": 25.356, "width_percent": 0.441}, {"sql": "select * from `levels` where `levels`.`id` = '1' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 166}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/BaseFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\BaseFilter.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilters.php", "line": 304}], "start": **********.206043, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:166", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=166", "ajax": false, "filename": "ResultResource.php", "line": "166"}, "connection": "racoed", "explain": null, "start_percent": 25.797, "width_percent": 0.42}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 173}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/BaseFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\BaseFilter.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilters.php", "line": 304}], "start": **********.2134252, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:173", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=173", "ajax": false, "filename": "ResultResource.php", "line": "173"}, "connection": "racoed", "explain": null, "start_percent": 26.217, "width_percent": 0.441}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '2' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 152}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.216924, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:152", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=152", "ajax": false, "filename": "ResultResource.php", "line": "152"}, "connection": "racoed", "explain": null, "start_percent": 26.658, "width_percent": 0.386}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 159}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.2205799, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:159", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=159", "ajax": false, "filename": "ResultResource.php", "line": "159"}, "connection": "racoed", "explain": null, "start_percent": 27.044, "width_percent": 0.496}, {"sql": "select * from `levels` where `levels`.`id` = '1' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 166}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.226109, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:166", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=166", "ajax": false, "filename": "ResultResource.php", "line": "166"}, "connection": "racoed", "explain": null, "start_percent": 27.539, "width_percent": 1.198}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 173}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.2317212, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:173", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=173", "ajax": false, "filename": "ResultResource.php", "line": "173"}, "connection": "racoed", "explain": null, "start_percent": 28.738, "width_percent": 0.448}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16') as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.239514, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:434", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=434", "ajax": false, "filename": "ResultResource.php", "line": "434"}, "connection": "racoed", "explain": null, "start_percent": 29.185, "width_percent": 0.606}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.251179, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 29.791, "width_percent": 1.24}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `school_session_id` = '2' and `semester_id` = '1' and exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "2", "1", "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.257673, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:422", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 422}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=422", "ajax": false, "filename": "ResultResource.php", "line": "422"}, "connection": "racoed", "explain": null, "start_percent": 31.031, "width_percent": 2.128}, {"sql": "select * from `applications` where `applications`.`user_id` = 22 and `applications`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 337}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 452}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.2677932, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:337", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=337", "ajax": false, "filename": "User.php", "line": "337"}, "connection": "racoed", "explain": null, "start_percent": 33.159, "width_percent": 0.551}, {"sql": "select * from `guardians` where `guardians`.`user_id` = 22 and `guardians`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, {"index": 22, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 316}, {"index": 23, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 452}, {"index": 24, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 136}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.273025, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "User.php:344", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\User.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=344", "ajax": false, "filename": "User.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 33.71, "width_percent": 2.493}, {"sql": "select * from `users` where `id` = 22 and `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '2' and `semester_id` = '1' and `level_id` = '1')) and `users`.`deleted_at` is null order by `users`.`id` asc", "type": "query", "params": [], "bindings": [22, 1, "2", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.2844782, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:108", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=108", "ajax": false, "filename": "HasRecords.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 36.203, "width_percent": 0.806}, {"sql": "select `semesters`.`name`, `semesters`.`id` from `semesters` inner join `registrations` on `registrations`.`semester_id` = `semesters`.`id` where `semesters`.`deleted_at` is null order by `semesters`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.3767822, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "racoed", "explain": null, "start_percent": 37.008, "width_percent": 1.763}, {"sql": "select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 25, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.392328, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:125", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=125", "ajax": false, "filename": "ResultResource.php", "line": "125"}, "connection": "racoed", "explain": null, "start_percent": 38.771, "width_percent": 1.825}, {"sql": "select * from `programmes` where `programmes`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 25, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.39765, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:125", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=125", "ajax": false, "filename": "ResultResource.php", "line": "125"}, "connection": "racoed", "explain": null, "start_percent": 40.596, "width_percent": 0.448}, {"sql": "select * from `departments` where `id` in (16, null)", "type": "query", "params": [], "bindings": [16, null], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Programme.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\Programme.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 125}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": **********.400744, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Programme.php:37", "source": {"index": 15, "namespace": null, "name": "app/Models/Programme.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Models\\Programme.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=37", "ajax": false, "filename": "Programme.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 41.044, "width_percent": 0.496}, {"sql": "select `name`, `id` from `departments` where `id` in (16) or `is_edu` = 1 or `is_gse` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [16, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 130}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.4046888, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:130", "source": {"index": 14, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=130", "ajax": false, "filename": "ResultResource.php", "line": "130"}, "connection": "racoed", "explain": null, "start_percent": 41.54, "width_percent": 0.537}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 22 and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [22, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 13}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.433835, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "filament.tables.result:13", "source": {"index": 14, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fresult.blade.php&line=13", "ajax": false, "filename": "result.blade.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 42.077, "width_percent": 0.847}, {"sql": "select `max_score`, `name` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 15}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.438132, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "filament.tables.result:15", "source": {"index": 17, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Ftables%2Fresult.blade.php&line=15", "ajax": false, "filename": "result.blade.php", "line": "15"}, "connection": "racoed", "explain": null, "start_percent": 42.924, "width_percent": 0.331}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = '2' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 192}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.443796, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:447", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=447", "ajax": false, "filename": "ResultResource.php", "line": "447"}, "connection": "racoed", "explain": null, "start_percent": 43.255, "width_percent": 0.854}, {"sql": "select * from `registrations` where `user_id` = 22 and `school_session_id` = '2' and `semester_id` = '1' and `level_id` = '1' limit 1", "type": "query", "params": [], "bindings": [22, "2", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 407}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 196}, {"index": 18, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.447655, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:407", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=407", "ajax": false, "filename": "ResultResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 44.109, "width_percent": 0.544}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `semester_id` = '1' and `level_id` = '1' and `department_id` = '16' order by `code` asc", "type": "query", "params": [], "bindings": ["1", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 207}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.45151, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:207", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=207", "ajax": false, "filename": "ResultResource.php", "line": "207"}, "connection": "racoed", "explain": null, "start_percent": 44.653, "width_percent": 0.895}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 209}, {"index": 17, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.4558318, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:209", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=209", "ajax": false, "filename": "ResultResource.php", "line": "209"}, "connection": "racoed", "explain": null, "start_percent": 45.548, "width_percent": 0.544}, {"sql": "select * from `scores` where `course_id` = 326 and `registration_id` = 35", "type": "query", "params": [], "bindings": [326, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.462221, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 46.092, "width_percent": 0.599}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.465647, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 46.691, "width_percent": 0.406}, {"sql": "select `total` from `total_scores` where `course_id` = 326 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [326, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.468852, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 47.097, "width_percent": 0.496}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 368}, {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.471768, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:368", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 368}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=368", "ajax": false, "filename": "ResultResource.php", "line": "368"}, "connection": "racoed", "explain": null, "start_percent": 47.593, "width_percent": 0.372}, {"sql": "select * from `scores` where `course_id` = 327 and `registration_id` = 35", "type": "query", "params": [], "bindings": [327, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.478093, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 47.965, "width_percent": 0.606}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.481387, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 48.571, "width_percent": 0.358}, {"sql": "select `total` from `total_scores` where `course_id` = 327 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [327, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.48433, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 48.929, "width_percent": 0.482}, {"sql": "select * from `scores` where `course_id` = 328 and `registration_id` = 35", "type": "query", "params": [], "bindings": [328, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.48748, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 49.411, "width_percent": 0.503}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.49267, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 49.914, "width_percent": 1.419}, {"sql": "select `total` from `total_scores` where `course_id` = 328 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [328, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.496926, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 51.333, "width_percent": 0.455}, {"sql": "select * from `scores` where `course_id` = 329 and `registration_id` = 35", "type": "query", "params": [], "bindings": [329, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.500203, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 51.787, "width_percent": 0.489}, {"sql": "select * from `assessments` where `assessments`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.503268, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 52.276, "width_percent": 0.344}, {"sql": "select `total` from `total_scores` where `course_id` = 329 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [329, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.50676, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 52.62, "width_percent": 1.102}, {"sql": "select * from `scores` where `course_id` = 330 and `registration_id` = 35", "type": "query", "params": [], "bindings": [330, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.51299, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 53.722, "width_percent": 0.578}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5163498, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 54.301, "width_percent": 0.413}, {"sql": "select `total` from `total_scores` where `course_id` = 330 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [330, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.519377, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 54.714, "width_percent": 0.468}, {"sql": "select * from `scores` where `course_id` = 331 and `registration_id` = 35", "type": "query", "params": [], "bindings": [331, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.523195, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 55.182, "width_percent": 0.716}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5284052, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 55.898, "width_percent": 0.448}, {"sql": "select `total` from `total_scores` where `course_id` = 331 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [331, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5314138, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 56.346, "width_percent": 0.53}, {"sql": "select * from `scores` where `course_id` = 332 and `registration_id` = 35", "type": "query", "params": [], "bindings": [332, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.534724, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 56.876, "width_percent": 0.537}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.537939, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 57.413, "width_percent": 0.372}, {"sql": "select `total` from `total_scores` where `course_id` = 332 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [332, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.542515, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 57.785, "width_percent": 1.653}, {"sql": "select * from `scores` where `course_id` = 333 and `registration_id` = 35", "type": "query", "params": [], "bindings": [333, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5475512, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 59.438, "width_percent": 0.558}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.551101, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 59.996, "width_percent": 0.386}, {"sql": "select `total` from `total_scores` where `course_id` = 333 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [333, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.553935, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 60.382, "width_percent": 0.455}, {"sql": "select * from `scores` where `course_id` = 334 and `registration_id` = 35", "type": "query", "params": [], "bindings": [334, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 21, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.559861, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 60.836, "width_percent": 1.494}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, {"index": 25, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 26, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.564477, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:216", "source": {"index": 20, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=216", "ajax": false, "filename": "ResultResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 62.33, "width_percent": 0.455}, {"sql": "select `total` from `total_scores` where `course_id` = 334 and `registration_id` = 35 limit 1", "type": "query", "params": [], "bindings": [334, 35], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 211}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.567377, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:220", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 220}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=220", "ajax": false, "filename": "ResultResource.php", "line": "220"}, "connection": "racoed", "explain": null, "start_percent": 62.785, "width_percent": 0.475}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 381}, {"index": 17, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.570625, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:381", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=381", "ajax": false, "filename": "ResultResource.php", "line": "381"}, "connection": "racoed", "explain": null, "start_percent": 63.26, "width_percent": 0.365}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 391}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 270}, {"index": 22, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 270}, {"index": 23, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.575572, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:391", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=391", "ajax": false, "filename": "ResultResource.php", "line": "391"}, "connection": "racoed", "explain": null, "start_percent": 63.625, "width_percent": 1.336}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.580013, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 64.961, "width_percent": 0.468}, {"sql": "select `credit` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5833802, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:315", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=315", "ajax": false, "filename": "ResultResource.php", "line": "315"}, "connection": "racoed", "explain": null, "start_percent": 65.429, "width_percent": 0.792}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.58718, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 66.221, "width_percent": 0.413}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.592082, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 66.635, "width_percent": 1.274}, {"sql": "select `credit` from `courses` where `level_id` = 2 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [2, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5965939, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:315", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 315}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=315", "ajax": false, "filename": "ResultResource.php", "line": "315"}, "connection": "racoed", "explain": null, "start_percent": 67.909, "width_percent": 0.751}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 307}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.600428, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 68.659, "width_percent": 0.468}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 277}, {"index": 17, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.603409, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:277", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=277", "ajax": false, "filename": "ResultResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 69.127, "width_percent": 0.317}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.606738, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 69.444, "width_percent": 1.054}, {"sql": "select `id`, `credit` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6121979, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:288", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=288", "ajax": false, "filename": "ResultResource.php", "line": "288"}, "connection": "racoed", "explain": null, "start_percent": 70.498, "width_percent": 0.758}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 326 limit 1", "type": "query", "params": [], "bindings": [35, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6161091, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 71.255, "width_percent": 0.475}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 327 limit 1", "type": "query", "params": [], "bindings": [35, 327], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.619092, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 71.731, "width_percent": 0.413}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 328 limit 1", "type": "query", "params": [], "bindings": [35, 328], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.622259, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 72.144, "width_percent": 0.399}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 329 limit 1", "type": "query", "params": [], "bindings": [35, 329], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.629083, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 72.543, "width_percent": 0.51}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 330 limit 1", "type": "query", "params": [], "bindings": [35, 330], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.632363, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 73.053, "width_percent": 0.468}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 331 limit 1", "type": "query", "params": [], "bindings": [35, 331], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.635479, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 73.521, "width_percent": 0.461}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 332 limit 1", "type": "query", "params": [], "bindings": [35, 332], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.638368, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 73.983, "width_percent": 0.413}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 333 limit 1", "type": "query", "params": [], "bindings": [35, 333], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6441631, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 74.396, "width_percent": 0.709}, {"sql": "select `total` from `total_scores` where `registration_id` = 35 and `course_id` = 334 limit 1", "type": "query", "params": [], "bindings": [35, 334], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.647699, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 75.105, "width_percent": 0.461}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 2 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [2, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.650671, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 75.566, "width_percent": 0.427}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 1 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 1, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.653698, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 75.993, "width_percent": 0.448}, {"sql": "select `id`, `credit` from `courses` where `level_id` = 2 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [2, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6581469, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:288", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=288", "ajax": false, "filename": "ResultResource.php", "line": "288"}, "connection": "racoed", "explain": null, "start_percent": 76.441, "width_percent": 2.1}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 343 limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.66385, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 78.541, "width_percent": 0.551}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 344 limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6670382, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 79.092, "width_percent": 0.441}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 345 limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6700032, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 79.533, "width_percent": 0.806}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 346 limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6748579, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 80.339, "width_percent": 1.364}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 347 limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.681314, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 81.702, "width_percent": 0.537}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 348 limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.684313, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 82.24, "width_percent": 0.441}, {"sql": "select `total` from `total_scores` where `registration_id` = 20 and `course_id` = 349 limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, {"index": 18, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 291}, {"index": 19, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.687242, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:245", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=245", "ajax": false, "filename": "ResultResource.php", "line": "245"}, "connection": "racoed", "explain": null, "start_percent": 82.68, "width_percent": 0.448}, {"sql": "select exists(select * from `scoresheets` where `school_session_id` = 3 and `semester_id` = 2 and `department_id` = 16 and `is_published` = 1) as `exists`", "type": "query", "params": [], "bindings": [3, 2, 16, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, {"index": 12, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 280}, {"index": 13, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 28}, {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.692275, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:469", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 469}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=469", "ajax": false, "filename": "ResultResource.php", "line": "469"}, "connection": "racoed", "explain": null, "start_percent": 83.128, "width_percent": 1.77}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 1 and `school_session_id` = 2 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [1, 2, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.697635, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 84.898, "width_percent": 0.489}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 2 and `school_session_id` = 2 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [2, 2, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.701057, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 85.387, "width_percent": 0.461}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 1 and `school_session_id` = 3 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [1, 3, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.70419, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 85.848, "width_percent": 0.427}, {"sql": "select exists(select * from `scoresheets` where (`semester_id` = 2 and `school_session_id` = 3 and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [2, 3, "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 330}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.708951, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:336", "source": {"index": 11, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=336", "ajax": false, "filename": "ResultResource.php", "line": "336"}, "connection": "racoed", "explain": null, "start_percent": 86.275, "width_percent": 1.742}, {"sql": "select `id`, `code` from `courses` where `level_id` = 1 and `semester_id` = 1 and `department_id` = '16'", "type": "query", "params": [], "bindings": [1, 1, "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 344}, {"index": 16, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.71425, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ResultResource.php:344", "source": {"index": 15, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=344", "ajax": false, "filename": "ResultResource.php", "line": "344"}, "connection": "racoed", "explain": null, "start_percent": 88.017, "width_percent": 0.792}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 326) limit 1", "type": "query", "params": [], "bindings": [35, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.718123, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 88.809, "width_percent": 0.461}, {"sql": "select `total` from `total_scores` where (`registration_id` = 35 and `course_id` = 327) limit 1", "type": "query", "params": [], "bindings": [35, 327], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, {"index": 18, "namespace": "view", "name": "filament.tables.result", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/tables/result.blade.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7210891, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ResultResource.php:350", "source": {"index": 17, "namespace": null, "name": "app/Filament/Student/Resources/ResultResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Resources\\ResultResource.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FResources%2FResultResource.php&line=350", "ajax": false, "filename": "ResultResource.php", "line": "350"}, "connection": "racoed", "explain": null, "start_percent": 89.271, "width_percent": 0.475}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726428, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.746, "width_percent": 1.336}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7290652, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.082, "width_percent": 0.434}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.730377, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.516, "width_percent": 0.399}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.73169, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.915, "width_percent": 0.475}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733069, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.39, "width_percent": 0.413}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7344532, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.804, "width_percent": 0.434}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.735829, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.237, "width_percent": 0.455}, {"sql": "select `id`, `code` from `courses` where `level_id` = ? and `semester_id` = ? and `department_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.737214, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.692, "width_percent": 0.702}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7398162, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.394, "width_percent": 0.799}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742439, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.193, "width_percent": 1.715}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745726, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.908, "width_percent": 0.448}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7470891, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.356, "width_percent": 0.427}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748546, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.783, "width_percent": 0.503}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75007, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.285, "width_percent": 0.482}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7515671, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.767, "width_percent": 0.572}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.767845, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.339, "width_percent": 0.661}]}, "models": {"data": {"App\\Models\\Course": {"value": 57, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\Grade": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\Score": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FScore.php&line=1", "ajax": false, "filename": "Score.php", "line": "?"}}, "App\\Models\\Assessment": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FAssessment.php&line=1", "ajax": false, "filename": "Assessment.php", "line": "?"}}, "App\\Models\\Registration": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\Department": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Level": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\Semester": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\Guardian": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGuardian.php&line=1", "ajax": false, "filename": "Guardian.php", "line": "?"}}, "App\\Models\\Programme": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=1", "ajax": false, "filename": "Programme.php", "line": "?"}}}, "count": 181, "is_counter": true}, "livewire": {"data": {"app.filament.student.resources.result-resource.pages.manage-results #lEM8ZaUlFr0QNFvA0tWE": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"result_filter\" => array:4 [\n        \"school_session_id\" => \"2\"\n        \"level_id\" => \"1\"\n        \"semester_id\" => \"1\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => null\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"result_filter\" => array:4 [\n        \"school_session_id\" => \"2\"\n        \"level_id\" => \"1\"\n        \"semester_id\" => \"1\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.student.resources.result-resource.pages.manage-results\"\n  \"component\" => \"App\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults\"\n  \"id\" => \"lEM8ZaUlFr0QNFvA0tWE\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Student\\Resources\\ResultResource\\Pages\\ManageResults@loadTable<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/CanDeferLoading.php:17-20</a>", "middleware": "web", "duration": "1.29s", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-803936324 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-803936324\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-777985181 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wW2oakzNb9e6FvC63LJYnnfnds4myZhJtt6SdRuH</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1880 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;result_filter&quot;:[{&quot;school_session_id&quot;:&quot;2&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:null,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;result_filter&quot;:[{&quot;school_session_id&quot;:&quot;2&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;lEM8ZaUlFr0QNFvA0tWE&quot;,&quot;name&quot;:&quot;app.filament.student.resources.result-resource.pages.manage-results&quot;,&quot;path&quot;:&quot;student\\/results&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;cf1d293fd272a77b95fec4c5159ed8542aea6f163e6f014127b808156938cc56&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">loadTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777985181\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-954825071 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6Ijg5Z1JRSFZsVkdlelViSnNMQVRGOUE9PSIsInZhbHVlIjoiNWF5VDJOZEpCcjFrSTAvMy9Bbk1jaWRFVlJJaTJtL1VXU0U5WWJFS0sxWG91TS85bFRXWG1KTGVxVnhUYkc1ZWhOME9CanJnVmxhOWhxcTVEUGRYZXJqRjh3RUtwV05vQ0VVQXlJYnNiTHM2bDkyWncxTkZLUmMzaTR0bklBSGYiLCJtYWMiOiI1ZGM3ZDIwMDNmYmUwNjU4MzgxNzU4Njg1Njk5NTQxZTY4MmM0YTZjZWJmNjI1OGNkOWQ3ZWIzZDJhMTc2MjZmIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6Ik11MGhsZ0pFc0YwaW5YbEpjN2N0OFE9PSIsInZhbHVlIjoic0dLb2NRNGlIYm1vcnBQZWFsSWxvTmk4Y2N3TytCZ1NvY3pQdWJaQTJJRisvTjVBSWVtczdESnJLQmxCMXlITno0ajVxd0wxeEdPTy96c0l5Tjd6NVArSmh4WlFTcUdHblZjVTNqYnVCRXhLd1RvUGlLemhNMkpKMUtaU0RUdFoiLCJtYWMiOiJlYmJkZmUyYTFmOTM4ZTJkYjI0MTQxN2I5OTNiMjk5YzBmZGM4ZmJlMGQ3ZjE0Mzk5NTQyM2JlNjIyNjY2MjgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"220 characters\">https://portal.racoed.test/student/results?tableFilters[result_filter][school_session_id]=2&amp;tableFilters[result_filter][level_id]=1&amp;tableFilters[result_filter][semester_id]=1&amp;tableFilters[result_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2273</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954825071\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1048548328 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wW2oakzNb9e6FvC63LJYnnfnds4myZhJtt6SdRuH</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8440Y34Bd5ZsZpI4FYdpE1CgYKG3KxlGlLXG6cgz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048548328\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-295078416 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 28 Sep 2025 06:08:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295078416\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-528173461 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wW2oakzNb9e6FvC63LJYnnfnds4myZhJtt6SdRuH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"252 characters\">https://portal.racoed.test/student/results?tableFilters%5Bresult_filter%5D%5Bschool_session_id%5D=2&amp;tableFilters%5Bresult_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"252 characters\">https://portal.racoed.test/student/results?tableFilters%5Bresult_filter%5D%5Bschool_session_id%5D=2&amp;tableFilters%5Bresult_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Bresult_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K67EZ0ENP6GKSYJZBY8G6N6Z</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageResults_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>result_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528173461\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}